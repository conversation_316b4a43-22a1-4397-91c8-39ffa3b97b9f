<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>log4net v2.0.3</Name>
  <Location>/Engine/Restricted/NotForLicensees/Source/Programs/AutomationTool/GauntletExtras/ThirdParty/log4net.2.0.3/</Location>
  <Date>2017-01-10T10:26:03.7613957-05:00</Date>
  <Function>Logging library used by Google APIs</Function>
  <Justification>Used internally for generating reports for Orion builds.</Justification>
  <Eula>http://logging.apache.org/log4net/license.html</Eula>
  <RedistributeTo>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>