<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Google API Client Library for .NET</Name>
  <Location>/Engine/Restricted/NotForLicensees/Source/Programs/AutomationTool/Gauntlet/ThirdParty/Google.Api*</Location>
  <Date>2017-02-28T18:00:58.8980501-05:00</Date>
  <Function>Is a runtime client for working with Google services.</Function>
  <Justification>Used internally for generating reports for builds.</Justification>
  <Eula>https://github.com/google/google-api-dotnet-client/blob/master/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>