<?xml version="1.0" encoding="utf-8"?>

<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <Name>FASTBuild for UE 4.21</Name>

  <Location>/Engine/Source/Programs/UnrealBuildTool/Executors/Experimental/FASTBuild.cs</Location>

  <Function>Adds support to use FASTBuild distributed compilation by UE4</Function>

  <Eula>https://github.com/liamkf/Unreal_FASTBuild/blob/master/LICENSE</Eula>

  <RedistributeTo>

    <EndUserGroup>Licensees</EndUserGroup>

    <EndUserGroup>Git</EndUserGroup>

    <EndUserGroup>P4</EndUserGroup>

  </RedistributeTo>

  <LicenseFolder>/UE4/Main/Engine/Source/ThirdParty/Licenses/UnrealFASTBuild_v0-95</LicenseFolder>

</TpsData>