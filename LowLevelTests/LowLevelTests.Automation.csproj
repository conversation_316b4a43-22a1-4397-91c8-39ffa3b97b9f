<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\Shared\UnrealEngine.csproj.props" />
  
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	<Configuration Condition=" '$(Configuration)' == '' ">Development</Configuration>
    <OutputType>Library</OutputType>
	<RootNamespace>LowLevelTests.Automation</RootNamespace>
    <AssemblyName>LowLevelTests.Automation</AssemblyName>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    <OutputPath>..\..\..\..\Binaries\DotNET\AutomationTool\LowLevelTests</OutputPath>
    <Configurations>Debug;Release;Development</Configurations>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <DebugType>pdbonly</DebugType>
	<SatelliteResourceLanguages>en</SatelliteResourceLanguages>    <!-- remove non english resource languages -->
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Development|AnyCPU' ">
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DefineConstants>$(DefineConstants);DEBUG;TRACE</DefineConstants>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Shared\EpicGames.Core\EpicGames.Core.csproj" PrivateAssets="All">
      <Private>false</Private>
    </ProjectReference>
    <ProjectReference Include="..\..\UnrealBuildTool\UnrealBuildTool.csproj" PrivateAssets="All">
      <Private>false</Private>
    </ProjectReference>
    <ProjectReference Include="..\..\AutomationTool\AutomationUtils\AutomationUtils.Automation.csproj" PrivateAssets="All">
      <Private>false</Private>
    </ProjectReference>
    <ProjectReference Include="..\..\AutomationTool\Scripts\AutomationScripts.Automation.csproj" PrivateAssets="All">
      <Private>false</Private>
    </ProjectReference>
    <ProjectReference Include="..\..\AutomationTool\Gauntlet\Gauntlet.Automation.csproj" PrivateAssets="All">
      <Private>false</Private>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="../../../../Platforms/*/Source/Programs/AutomationTool/LowLevelTests/**/*.cs">
      <Link>Platform/$([System.Text.RegularExpressions.Regex]::Replace(%(Compile.RecursiveDir), [\\/].+$, ``))/%(Compile.FileName).cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="MySql.Data" Version="6.10.9" PrivateAssets="all" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Utility\" />
  </ItemGroup>
</Project>
