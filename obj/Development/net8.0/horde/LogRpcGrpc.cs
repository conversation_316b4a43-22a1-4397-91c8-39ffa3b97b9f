// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: horde/log_rpc.proto
// </auto-generated>
// Original file comments:
// Copyright Epic Games, Inc. All Rights Reserved.
//
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace Horde.Common.Rpc {
  public static partial class LogRpc
  {
    static readonly string __ServiceName = "Horde.LogRpc";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Horde.Common.Rpc.RpcUpdateLogRequest> __Marshaller_Horde_RpcUpdateLogRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Horde.Common.Rpc.RpcUpdateLogRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Horde.Common.Rpc.RpcUpdateLogResponse> __Marshaller_Horde_RpcUpdateLogResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Horde.Common.Rpc.RpcUpdateLogResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Horde.Common.Rpc.RpcUpdateLogTailRequest> __Marshaller_Horde_RpcUpdateLogTailRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Horde.Common.Rpc.RpcUpdateLogTailRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Horde.Common.Rpc.RpcUpdateLogTailResponse> __Marshaller_Horde_RpcUpdateLogTailResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Horde.Common.Rpc.RpcUpdateLogTailResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Horde.Common.Rpc.RpcCreateLogEventsRequest> __Marshaller_Horde_RpcCreateLogEventsRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Horde.Common.Rpc.RpcCreateLogEventsRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Google.Protobuf.WellKnownTypes.Empty> __Marshaller_google_protobuf_Empty = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Google.Protobuf.WellKnownTypes.Empty.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Horde.Common.Rpc.RpcUpdateLogRequest, global::Horde.Common.Rpc.RpcUpdateLogResponse> __Method_UpdateLog = new grpc::Method<global::Horde.Common.Rpc.RpcUpdateLogRequest, global::Horde.Common.Rpc.RpcUpdateLogResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "UpdateLog",
        __Marshaller_Horde_RpcUpdateLogRequest,
        __Marshaller_Horde_RpcUpdateLogResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Horde.Common.Rpc.RpcUpdateLogTailRequest, global::Horde.Common.Rpc.RpcUpdateLogTailResponse> __Method_UpdateLogTail = new grpc::Method<global::Horde.Common.Rpc.RpcUpdateLogTailRequest, global::Horde.Common.Rpc.RpcUpdateLogTailResponse>(
        grpc::MethodType.DuplexStreaming,
        __ServiceName,
        "UpdateLogTail",
        __Marshaller_Horde_RpcUpdateLogTailRequest,
        __Marshaller_Horde_RpcUpdateLogTailResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Horde.Common.Rpc.RpcCreateLogEventsRequest, global::Google.Protobuf.WellKnownTypes.Empty> __Method_CreateLogEvents = new grpc::Method<global::Horde.Common.Rpc.RpcCreateLogEventsRequest, global::Google.Protobuf.WellKnownTypes.Empty>(
        grpc::MethodType.Unary,
        __ServiceName,
        "CreateLogEvents",
        __Marshaller_Horde_RpcCreateLogEventsRequest,
        __Marshaller_google_protobuf_Empty);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::Horde.Common.Rpc.LogRpcReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of LogRpc</summary>
    [grpc::BindServiceMethod(typeof(LogRpc), "BindService")]
    public abstract partial class LogRpcBase
    {
      /// <summary>
      /// Update the current log state
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Horde.Common.Rpc.RpcUpdateLogResponse> UpdateLog(global::Horde.Common.Rpc.RpcUpdateLogRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// Long poll for requests to return unflushed log tail data
      /// </summary>
      /// <param name="requestStream">Used for reading requests from the client.</param>
      /// <param name="responseStream">Used for sending responses back to the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>A task indicating completion of the handler.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task UpdateLogTail(grpc::IAsyncStreamReader<global::Horde.Common.Rpc.RpcUpdateLogTailRequest> requestStream, grpc::IServerStreamWriter<global::Horde.Common.Rpc.RpcUpdateLogTailResponse> responseStream, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// Creates events for a log file, highlighting particular lines of interest
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::Google.Protobuf.WellKnownTypes.Empty> CreateLogEvents(global::Horde.Common.Rpc.RpcCreateLogEventsRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for LogRpc</summary>
    public partial class LogRpcClient : grpc::ClientBase<LogRpcClient>
    {
      /// <summary>Creates a new client for LogRpc</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public LogRpcClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for LogRpc that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public LogRpcClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected LogRpcClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected LogRpcClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// Update the current log state
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Horde.Common.Rpc.RpcUpdateLogResponse UpdateLog(global::Horde.Common.Rpc.RpcUpdateLogRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return UpdateLog(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Update the current log state
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Horde.Common.Rpc.RpcUpdateLogResponse UpdateLog(global::Horde.Common.Rpc.RpcUpdateLogRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_UpdateLog, null, options, request);
      }
      /// <summary>
      /// Update the current log state
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Horde.Common.Rpc.RpcUpdateLogResponse> UpdateLogAsync(global::Horde.Common.Rpc.RpcUpdateLogRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return UpdateLogAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Update the current log state
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Horde.Common.Rpc.RpcUpdateLogResponse> UpdateLogAsync(global::Horde.Common.Rpc.RpcUpdateLogRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_UpdateLog, null, options, request);
      }
      /// <summary>
      /// Long poll for requests to return unflushed log tail data
      /// </summary>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncDuplexStreamingCall<global::Horde.Common.Rpc.RpcUpdateLogTailRequest, global::Horde.Common.Rpc.RpcUpdateLogTailResponse> UpdateLogTail(grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return UpdateLogTail(new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Long poll for requests to return unflushed log tail data
      /// </summary>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncDuplexStreamingCall<global::Horde.Common.Rpc.RpcUpdateLogTailRequest, global::Horde.Common.Rpc.RpcUpdateLogTailResponse> UpdateLogTail(grpc::CallOptions options)
      {
        return CallInvoker.AsyncDuplexStreamingCall(__Method_UpdateLogTail, null, options);
      }
      /// <summary>
      /// Creates events for a log file, highlighting particular lines of interest
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Google.Protobuf.WellKnownTypes.Empty CreateLogEvents(global::Horde.Common.Rpc.RpcCreateLogEventsRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CreateLogEvents(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Creates events for a log file, highlighting particular lines of interest
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::Google.Protobuf.WellKnownTypes.Empty CreateLogEvents(global::Horde.Common.Rpc.RpcCreateLogEventsRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_CreateLogEvents, null, options, request);
      }
      /// <summary>
      /// Creates events for a log file, highlighting particular lines of interest
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Google.Protobuf.WellKnownTypes.Empty> CreateLogEventsAsync(global::Horde.Common.Rpc.RpcCreateLogEventsRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CreateLogEventsAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Creates events for a log file, highlighting particular lines of interest
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::Google.Protobuf.WellKnownTypes.Empty> CreateLogEventsAsync(global::Horde.Common.Rpc.RpcCreateLogEventsRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_CreateLogEvents, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override LogRpcClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new LogRpcClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(LogRpcBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_UpdateLog, serviceImpl.UpdateLog)
          .AddMethod(__Method_UpdateLogTail, serviceImpl.UpdateLogTail)
          .AddMethod(__Method_CreateLogEvents, serviceImpl.CreateLogEvents).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, LogRpcBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_UpdateLog, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Horde.Common.Rpc.RpcUpdateLogRequest, global::Horde.Common.Rpc.RpcUpdateLogResponse>(serviceImpl.UpdateLog));
      serviceBinder.AddMethod(__Method_UpdateLogTail, serviceImpl == null ? null : new grpc::DuplexStreamingServerMethod<global::Horde.Common.Rpc.RpcUpdateLogTailRequest, global::Horde.Common.Rpc.RpcUpdateLogTailResponse>(serviceImpl.UpdateLogTail));
      serviceBinder.AddMethod(__Method_CreateLogEvents, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Horde.Common.Rpc.RpcCreateLogEventsRequest, global::Google.Protobuf.WellKnownTypes.Empty>(serviceImpl.CreateLogEvents));
    }

  }
}
#endregion
