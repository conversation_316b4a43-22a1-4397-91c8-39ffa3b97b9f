// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: horde/log_rpc_messages.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Horde.Common.Rpc {

  /// <summary>Holder for reflection information generated from horde/log_rpc_messages.proto</summary>
  public static partial class LogRpcMessagesReflection {

    #region Descriptor
    /// <summary>File descriptor for horde/log_rpc_messages.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static LogRpcMessagesReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Chxob3JkZS9sb2dfcnBjX21lc3NhZ2VzLnByb3RvEgVIb3JkZSJ0ChNScGNV",
            "cGRhdGVMb2dSZXF1ZXN0Eg0KBUxvZ0lkGAEgASgJEhIKClRhcmdldEhhc2gY",
            "BSABKAkSFQoNVGFyZ2V0TG9jYXRvchgCIAEoCRIRCglMaW5lQ291bnQYAyAB",
            "KAUSEAoIQ29tcGxldGUYBCABKAgiFgoUUnBjVXBkYXRlTG9nUmVzcG9uc2Ui",
            "TAoXUnBjVXBkYXRlTG9nVGFpbFJlcXVlc3QSDQoFTG9nSWQYASABKAkSEAoI",
            "VGFpbE5leHQYAiABKAUSEAoIVGFpbERhdGEYAyABKAwiLAoYUnBjVXBkYXRl",
            "TG9nVGFpbFJlc3BvbnNlEhAKCFRhaWxOZXh0GAEgASgFIkwKGVJwY0NyZWF0",
            "ZUxvZ0V2ZW50c1JlcXVlc3QSLwoGRXZlbnRzGAEgAygLMh8uSG9yZGUuUnBj",
            "Q3JlYXRlTG9nRXZlbnRSZXF1ZXN0ImEKGFJwY0NyZWF0ZUxvZ0V2ZW50UmVx",
            "dWVzdBIQCghTZXZlcml0eRgCIAEoBRINCgVMb2dJZBgEIAEoCRIRCglMaW5l",
            "SW5kZXgYBSABKAUSEQoJTGluZUNvdW50GAYgASgFQhOqAhBIb3JkZS5Db21t",
            "b24uUnBjYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Horde.Common.Rpc.RpcUpdateLogRequest), global::Horde.Common.Rpc.RpcUpdateLogRequest.Parser, new[]{ "LogId", "TargetHash", "TargetLocator", "LineCount", "Complete" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Horde.Common.Rpc.RpcUpdateLogResponse), global::Horde.Common.Rpc.RpcUpdateLogResponse.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Horde.Common.Rpc.RpcUpdateLogTailRequest), global::Horde.Common.Rpc.RpcUpdateLogTailRequest.Parser, new[]{ "LogId", "TailNext", "TailData" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Horde.Common.Rpc.RpcUpdateLogTailResponse), global::Horde.Common.Rpc.RpcUpdateLogTailResponse.Parser, new[]{ "TailNext" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Horde.Common.Rpc.RpcCreateLogEventsRequest), global::Horde.Common.Rpc.RpcCreateLogEventsRequest.Parser, new[]{ "Events" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Horde.Common.Rpc.RpcCreateLogEventRequest), global::Horde.Common.Rpc.RpcCreateLogEventRequest.Parser, new[]{ "Severity", "LogId", "LineIndex", "LineCount" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class RpcUpdateLogRequest : pb::IMessage<RpcUpdateLogRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RpcUpdateLogRequest> _parser = new pb::MessageParser<RpcUpdateLogRequest>(() => new RpcUpdateLogRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RpcUpdateLogRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Horde.Common.Rpc.LogRpcMessagesReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogRequest(RpcUpdateLogRequest other) : this() {
      logId_ = other.logId_;
      targetHash_ = other.targetHash_;
      targetLocator_ = other.targetLocator_;
      lineCount_ = other.lineCount_;
      complete_ = other.complete_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogRequest Clone() {
      return new RpcUpdateLogRequest(this);
    }

    /// <summary>Field number for the "LogId" field.</summary>
    public const int LogIdFieldNumber = 1;
    private string logId_ = "";
    /// <summary>
    /// The unique log id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LogId {
      get { return logId_; }
      set {
        logId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "TargetHash" field.</summary>
    public const int TargetHashFieldNumber = 5;
    private string targetHash_ = "";
    /// <summary>
    /// Hash of the latest flushed node
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string TargetHash {
      get { return targetHash_; }
      set {
        targetHash_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "TargetLocator" field.</summary>
    public const int TargetLocatorFieldNumber = 2;
    private string targetLocator_ = "";
    /// <summary>
    /// Locator for the latest flushed node
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string TargetLocator {
      get { return targetLocator_; }
      set {
        targetLocator_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "LineCount" field.</summary>
    public const int LineCountFieldNumber = 3;
    private int lineCount_;
    /// <summary>
    /// Number of lines that have been flushed
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int LineCount {
      get { return lineCount_; }
      set {
        lineCount_ = value;
      }
    }

    /// <summary>Field number for the "Complete" field.</summary>
    public const int CompleteFieldNumber = 4;
    private bool complete_;
    /// <summary>
    /// Whether the log is complete
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Complete {
      get { return complete_; }
      set {
        complete_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RpcUpdateLogRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RpcUpdateLogRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LogId != other.LogId) return false;
      if (TargetHash != other.TargetHash) return false;
      if (TargetLocator != other.TargetLocator) return false;
      if (LineCount != other.LineCount) return false;
      if (Complete != other.Complete) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LogId.Length != 0) hash ^= LogId.GetHashCode();
      if (TargetHash.Length != 0) hash ^= TargetHash.GetHashCode();
      if (TargetLocator.Length != 0) hash ^= TargetLocator.GetHashCode();
      if (LineCount != 0) hash ^= LineCount.GetHashCode();
      if (Complete != false) hash ^= Complete.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LogId);
      }
      if (TargetLocator.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(TargetLocator);
      }
      if (LineCount != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(LineCount);
      }
      if (Complete != false) {
        output.WriteRawTag(32);
        output.WriteBool(Complete);
      }
      if (TargetHash.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(TargetHash);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LogId);
      }
      if (TargetLocator.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(TargetLocator);
      }
      if (LineCount != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(LineCount);
      }
      if (Complete != false) {
        output.WriteRawTag(32);
        output.WriteBool(Complete);
      }
      if (TargetHash.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(TargetHash);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LogId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LogId);
      }
      if (TargetHash.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(TargetHash);
      }
      if (TargetLocator.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(TargetLocator);
      }
      if (LineCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(LineCount);
      }
      if (Complete != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RpcUpdateLogRequest other) {
      if (other == null) {
        return;
      }
      if (other.LogId.Length != 0) {
        LogId = other.LogId;
      }
      if (other.TargetHash.Length != 0) {
        TargetHash = other.TargetHash;
      }
      if (other.TargetLocator.Length != 0) {
        TargetLocator = other.TargetLocator;
      }
      if (other.LineCount != 0) {
        LineCount = other.LineCount;
      }
      if (other.Complete != false) {
        Complete = other.Complete;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            LogId = input.ReadString();
            break;
          }
          case 18: {
            TargetLocator = input.ReadString();
            break;
          }
          case 24: {
            LineCount = input.ReadInt32();
            break;
          }
          case 32: {
            Complete = input.ReadBool();
            break;
          }
          case 42: {
            TargetHash = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            LogId = input.ReadString();
            break;
          }
          case 18: {
            TargetLocator = input.ReadString();
            break;
          }
          case 24: {
            LineCount = input.ReadInt32();
            break;
          }
          case 32: {
            Complete = input.ReadBool();
            break;
          }
          case 42: {
            TargetHash = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class RpcUpdateLogResponse : pb::IMessage<RpcUpdateLogResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RpcUpdateLogResponse> _parser = new pb::MessageParser<RpcUpdateLogResponse>(() => new RpcUpdateLogResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RpcUpdateLogResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Horde.Common.Rpc.LogRpcMessagesReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogResponse(RpcUpdateLogResponse other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogResponse Clone() {
      return new RpcUpdateLogResponse(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RpcUpdateLogResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RpcUpdateLogResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RpcUpdateLogResponse other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  public sealed partial class RpcUpdateLogTailRequest : pb::IMessage<RpcUpdateLogTailRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RpcUpdateLogTailRequest> _parser = new pb::MessageParser<RpcUpdateLogTailRequest>(() => new RpcUpdateLogTailRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RpcUpdateLogTailRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Horde.Common.Rpc.LogRpcMessagesReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogTailRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogTailRequest(RpcUpdateLogTailRequest other) : this() {
      logId_ = other.logId_;
      tailNext_ = other.tailNext_;
      tailData_ = other.tailData_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogTailRequest Clone() {
      return new RpcUpdateLogTailRequest(this);
    }

    /// <summary>Field number for the "LogId" field.</summary>
    public const int LogIdFieldNumber = 1;
    private string logId_ = "";
    /// <summary>
    /// The unique log id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LogId {
      get { return logId_; }
      set {
        logId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "TailNext" field.</summary>
    public const int TailNextFieldNumber = 2;
    private int tailNext_;
    /// <summary>
    /// Starting line index of the new tail data
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TailNext {
      get { return tailNext_; }
      set {
        tailNext_ = value;
      }
    }

    /// <summary>Field number for the "TailData" field.</summary>
    public const int TailDataFieldNumber = 3;
    private pb::ByteString tailData_ = pb::ByteString.Empty;
    /// <summary>
    /// New tail data to append (from LineCount backwards)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString TailData {
      get { return tailData_; }
      set {
        tailData_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RpcUpdateLogTailRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RpcUpdateLogTailRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LogId != other.LogId) return false;
      if (TailNext != other.TailNext) return false;
      if (TailData != other.TailData) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LogId.Length != 0) hash ^= LogId.GetHashCode();
      if (TailNext != 0) hash ^= TailNext.GetHashCode();
      if (TailData.Length != 0) hash ^= TailData.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LogId);
      }
      if (TailNext != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(TailNext);
      }
      if (TailData.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(TailData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(LogId);
      }
      if (TailNext != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(TailNext);
      }
      if (TailData.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(TailData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LogId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LogId);
      }
      if (TailNext != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TailNext);
      }
      if (TailData.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(TailData);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RpcUpdateLogTailRequest other) {
      if (other == null) {
        return;
      }
      if (other.LogId.Length != 0) {
        LogId = other.LogId;
      }
      if (other.TailNext != 0) {
        TailNext = other.TailNext;
      }
      if (other.TailData.Length != 0) {
        TailData = other.TailData;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            LogId = input.ReadString();
            break;
          }
          case 16: {
            TailNext = input.ReadInt32();
            break;
          }
          case 26: {
            TailData = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            LogId = input.ReadString();
            break;
          }
          case 16: {
            TailNext = input.ReadInt32();
            break;
          }
          case 26: {
            TailData = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class RpcUpdateLogTailResponse : pb::IMessage<RpcUpdateLogTailResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RpcUpdateLogTailResponse> _parser = new pb::MessageParser<RpcUpdateLogTailResponse>(() => new RpcUpdateLogTailResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RpcUpdateLogTailResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Horde.Common.Rpc.LogRpcMessagesReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogTailResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogTailResponse(RpcUpdateLogTailResponse other) : this() {
      tailNext_ = other.tailNext_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcUpdateLogTailResponse Clone() {
      return new RpcUpdateLogTailResponse(this);
    }

    /// <summary>Field number for the "TailNext" field.</summary>
    public const int TailNextFieldNumber = 1;
    private int tailNext_;
    /// <summary>
    /// Index of the next requested tail line, or -1 if tailing is not desired.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TailNext {
      get { return tailNext_; }
      set {
        tailNext_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RpcUpdateLogTailResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RpcUpdateLogTailResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (TailNext != other.TailNext) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (TailNext != 0) hash ^= TailNext.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (TailNext != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(TailNext);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (TailNext != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(TailNext);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (TailNext != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TailNext);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RpcUpdateLogTailResponse other) {
      if (other == null) {
        return;
      }
      if (other.TailNext != 0) {
        TailNext = other.TailNext;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            TailNext = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            TailNext = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class RpcCreateLogEventsRequest : pb::IMessage<RpcCreateLogEventsRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RpcCreateLogEventsRequest> _parser = new pb::MessageParser<RpcCreateLogEventsRequest>(() => new RpcCreateLogEventsRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RpcCreateLogEventsRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Horde.Common.Rpc.LogRpcMessagesReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcCreateLogEventsRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcCreateLogEventsRequest(RpcCreateLogEventsRequest other) : this() {
      events_ = other.events_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcCreateLogEventsRequest Clone() {
      return new RpcCreateLogEventsRequest(this);
    }

    /// <summary>Field number for the "Events" field.</summary>
    public const int EventsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Horde.Common.Rpc.RpcCreateLogEventRequest> _repeated_events_codec
        = pb::FieldCodec.ForMessage(10, global::Horde.Common.Rpc.RpcCreateLogEventRequest.Parser);
    private readonly pbc::RepeatedField<global::Horde.Common.Rpc.RpcCreateLogEventRequest> events_ = new pbc::RepeatedField<global::Horde.Common.Rpc.RpcCreateLogEventRequest>();
    /// <summary>
    /// List of events to send
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Horde.Common.Rpc.RpcCreateLogEventRequest> Events {
      get { return events_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RpcCreateLogEventsRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RpcCreateLogEventsRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!events_.Equals(other.events_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= events_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      events_.WriteTo(output, _repeated_events_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      events_.WriteTo(ref output, _repeated_events_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += events_.CalculateSize(_repeated_events_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RpcCreateLogEventsRequest other) {
      if (other == null) {
        return;
      }
      events_.Add(other.events_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            events_.AddEntriesFrom(input, _repeated_events_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            events_.AddEntriesFrom(ref input, _repeated_events_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class RpcCreateLogEventRequest : pb::IMessage<RpcCreateLogEventRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RpcCreateLogEventRequest> _parser = new pb::MessageParser<RpcCreateLogEventRequest>(() => new RpcCreateLogEventRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RpcCreateLogEventRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Horde.Common.Rpc.LogRpcMessagesReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcCreateLogEventRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcCreateLogEventRequest(RpcCreateLogEventRequest other) : this() {
      severity_ = other.severity_;
      logId_ = other.logId_;
      lineIndex_ = other.lineIndex_;
      lineCount_ = other.lineCount_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RpcCreateLogEventRequest Clone() {
      return new RpcCreateLogEventRequest(this);
    }

    /// <summary>Field number for the "Severity" field.</summary>
    public const int SeverityFieldNumber = 2;
    private int severity_;
    /// <summary>
    /// Severity of this event
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Severity {
      get { return severity_; }
      set {
        severity_ = value;
      }
    }

    /// <summary>Field number for the "LogId" field.</summary>
    public const int LogIdFieldNumber = 4;
    private string logId_ = "";
    /// <summary>
    /// Unique id of the log containing this event
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LogId {
      get { return logId_; }
      set {
        logId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "LineIndex" field.</summary>
    public const int LineIndexFieldNumber = 5;
    private int lineIndex_;
    /// <summary>
    /// Index of the first line relating to this event
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int LineIndex {
      get { return lineIndex_; }
      set {
        lineIndex_ = value;
      }
    }

    /// <summary>Field number for the "LineCount" field.</summary>
    public const int LineCountFieldNumber = 6;
    private int lineCount_;
    /// <summary>
    /// Number of lines in this event
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int LineCount {
      get { return lineCount_; }
      set {
        lineCount_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RpcCreateLogEventRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RpcCreateLogEventRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Severity != other.Severity) return false;
      if (LogId != other.LogId) return false;
      if (LineIndex != other.LineIndex) return false;
      if (LineCount != other.LineCount) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Severity != 0) hash ^= Severity.GetHashCode();
      if (LogId.Length != 0) hash ^= LogId.GetHashCode();
      if (LineIndex != 0) hash ^= LineIndex.GetHashCode();
      if (LineCount != 0) hash ^= LineCount.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Severity != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Severity);
      }
      if (LogId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(LogId);
      }
      if (LineIndex != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(LineIndex);
      }
      if (LineCount != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(LineCount);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Severity != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Severity);
      }
      if (LogId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(LogId);
      }
      if (LineIndex != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(LineIndex);
      }
      if (LineCount != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(LineCount);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Severity != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Severity);
      }
      if (LogId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LogId);
      }
      if (LineIndex != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(LineIndex);
      }
      if (LineCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(LineCount);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RpcCreateLogEventRequest other) {
      if (other == null) {
        return;
      }
      if (other.Severity != 0) {
        Severity = other.Severity;
      }
      if (other.LogId.Length != 0) {
        LogId = other.LogId;
      }
      if (other.LineIndex != 0) {
        LineIndex = other.LineIndex;
      }
      if (other.LineCount != 0) {
        LineCount = other.LineCount;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 16: {
            Severity = input.ReadInt32();
            break;
          }
          case 34: {
            LogId = input.ReadString();
            break;
          }
          case 40: {
            LineIndex = input.ReadInt32();
            break;
          }
          case 48: {
            LineCount = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 16: {
            Severity = input.ReadInt32();
            break;
          }
          case 34: {
            LogId = input.ReadString();
            break;
          }
          case 40: {
            LineIndex = input.ReadInt32();
            break;
          }
          case 48: {
            LineCount = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
