// Copyright Epic Games, Inc. All Rights Reserved.

#include "WuXiaPlayerController.h"
#include "GameFramework/Pawn.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "NavigationSystem.h"
#include "WuXiaCharacter.h"
#include "Camera/CameraComponent.h"
#include "Character/WX_PlayerCharacterUnit.h"
#include "Character/WX_CharacterController.h"
#include "Component/WX_FollowSystemComponent.h"
#include "Engine/LocalPlayer.h"
#include "Engine/OverlapResult.h"
#include "UI/WX_UIHUD.h"

DEFINE_LOG_CATEGORY(LogTemplateCharacter);

AWuXiaPlayerController::AWuXiaPlayerController()
{
	bShowMouseCursor = true;
	DefaultMouseCursor = EMouseCursor::Default;
	bEnableClickEvents=true;
	FollowSystemComponentClass=CreateDefaultSubobject<UWX_FollowSystemComponent>(TEXT("FollowSystem"));
	
}

void AWuXiaPlayerController::BeginPlay()
{
	Super::BeginPlay();
}

void AWuXiaPlayerController::OnPossess(APawn* InPawn)
{
	Super::OnPossess(InPawn);
	ControlledPawn = Cast<AWuXiaCharacter>(InPawn);
	check(ControlledPawn);
	
	// set the zoom level from the pawn's camera
	DefaultZoom = CameraZoom = ControlledPawn->GetCamera()->OrthoWidth;

	// cast the HUD pointer
	StrategyHUD = Cast<AWX_UIHUD>(GetHUD());
	check(StrategyHUD);
}

void AWuXiaPlayerController::SetupInputComponent()
{
	// set up gameplay key bindings
	Super::SetupInputComponent();

	// Add Input Mapping Context
	if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(GetLocalPlayer()))
	{
		Subsystem->AddMappingContext(DefaultMappingContext, 0);
	}

	// Set up action bindings
	if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(InputComponent))
	{
	# pragma region Mouse Interaction
		EnhancedInputComponent->BindAction(SelectHoldAction, ETriggerEvent::Started, this, &AWuXiaPlayerController::SelectHoldStarted);
		EnhancedInputComponent->BindAction(SelectHoldAction, ETriggerEvent::Triggered, this, &AWuXiaPlayerController::SelectHoldTriggered);
		EnhancedInputComponent->BindAction(SelectHoldAction, ETriggerEvent::Completed, this, &AWuXiaPlayerController::SelectHoldCompleted);
		EnhancedInputComponent->BindAction(SelectHoldAction, ETriggerEvent::Canceled, this, &AWuXiaPlayerController::SelectHoldCompleted);

		EnhancedInputComponent->BindAction(SelectClickAction, ETriggerEvent::Completed, this, &AWuXiaPlayerController::SelectClick);

		EnhancedInputComponent->BindAction(SelectionModifierAction, ETriggerEvent::Triggered, this, &AWuXiaPlayerController::SelectionModifier);
		EnhancedInputComponent->BindAction(SelectionModifierAction, ETriggerEvent::Completed, this, &AWuXiaPlayerController::SelectionModifier);
		EnhancedInputComponent->BindAction(SelectionModifierAction, ETriggerEvent::Canceled, this, &AWuXiaPlayerController::SelectionModifier);

		EnhancedInputComponent->BindAction(InteractHoldAction, ETriggerEvent::Started, this, &AWuXiaPlayerController::InteractHoldStarted);
		EnhancedInputComponent->BindAction(InteractHoldAction, ETriggerEvent::Triggered, this, &AWuXiaPlayerController::InteractHoldTriggered);

		EnhancedInputComponent->BindAction(InteractClickAction, ETriggerEvent::Started, this, &AWuXiaPlayerController::InteractClickStarted);
		EnhancedInputComponent->BindAction(InteractClickAction, ETriggerEvent::Completed, this, &AWuXiaPlayerController::InteractClickCompleted);
#pragma endregion Mouse Interaction

#pragma region Camera Interaction
		// Camera
		EnhancedInputComponent->BindAction(MoveCameraAction, ETriggerEvent::Triggered, this, &AWuXiaPlayerController::MoveCamera);
		EnhancedInputComponent->BindAction(ZoomCameraAction, ETriggerEvent::Triggered, this, &AWuXiaPlayerController::ZoomCamera);
		EnhancedInputComponent->BindAction(ResetCameraAction, ETriggerEvent::Triggered, this, &AWuXiaPlayerController::ResetCamera);
#pragma endregion	
	}
	else
	{
		UE_LOG(LogTemplateCharacter, Error, TEXT("'%s' Failed to find an Enhanced Input Component! This template is built to use the Enhanced Input system. If you intend to use the legacy system, then you will need to update this C++ file."), *GetNameSafe(this));
	}
}

FVector2D AWuXiaPlayerController::GetMouseLocation()
{
	// attempt to get the mouse position from this PC
	float MouseX, MouseY;

	if (GetMousePosition(MouseX, MouseY))
	{
		return FVector2D(MouseX, MouseY);
	}

	// return an invalid vector
	return FVector2D::ZeroVector;
}

bool AWuXiaPlayerController::GetLocationUnderCursor(FVector& Location)
{
	FHitResult OutHit;

	GetHitResultUnderCursorByChannel(SelectionTraceChannel, true, OutHit);

	// if there was a blocking hit, return the hit location
	if (OutHit.bBlockingHit)
	{
		Location = OutHit.Location;
		return true;
	}

	return OutHit.bBlockingHit;
}

#pragma region mouse
// Triggered every frame when the input is held down
void AWuXiaPlayerController::SelectClick(const FInputActionValue& Value)
{
	DoSelectionCommand();
}

void AWuXiaPlayerController::SelectHoldStarted(const FInputActionValue& Value)
{
	// save the selection start position
	StartingSelectionPosition = GetMouseLocation();
}

void AWuXiaPlayerController::SelectHoldTriggered(const FInputActionValue& Value)
{
	// get the current mouse position
	FVector2D SelectionPosition = GetMouseLocation();

	// calculate the size of the selection box
	FVector2D SelectionSize = SelectionPosition - StartingSelectionPosition;

	// // update the selection box on the HUD
	StrategyHUD->DragSelectUpdate(StartingSelectionPosition, SelectionSize, SelectionPosition, true);
}

void AWuXiaPlayerController::SelectHoldCompleted(const FInputActionValue& Value)
{
	// reset the drag box on the HUD
	StrategyHUD->DragSelectUpdate(FVector2D::ZeroVector, FVector2D::ZeroVector, FVector2D::ZeroVector, false);
}

void AWuXiaPlayerController::SelectionModifier(const FInputActionValue& Value)
{
	// save the starting interaction position
	bSelectionModifier = Value.Get<bool>();
}

void AWuXiaPlayerController::InteractHoldStarted(const FInputActionValue& Value)
{
	StartingInteractionPosition = GetMouseLocation();
}

void AWuXiaPlayerController::DoDragScrollCommand()
{
	// choose the cursor position based on the input mode
	FVector2D WorkingPosition;
	
	// read the mouse position
	bool bResult = GetMousePosition(WorkingPosition.X, WorkingPosition.Y);
	
	// find the difference between the starting interaction position and current coords
	const FVector2D InteractionDelta = StartingInteractionPosition - WorkingPosition;

	const FRotator CameraRot(0.0f, -45.0f, 0.0f);

	// rotate and scale the interaction delta
	const FVector ScrollDelta = CameraRot.RotateVector(FVector(InteractionDelta.X, InteractionDelta.Y, 0.0f)) * DragMultiplier;

	// apply the world offset to the controlled pawn
	ControlledPawn->AddActorWorldOffset(ScrollDelta);
}

void AWuXiaPlayerController::InteractHoldTriggered(const FInputActionValue& Value)
{
	// do a drag scroll 
	DoDragScrollCommand();
}

void AWuXiaPlayerController::InteractClickStarted(const FInputActionValue& Value)
{
	// reset the interaction flag
	ResetInteraction();
}

void AWuXiaPlayerController::InteractClickCompleted(const FInputActionValue& Value)
{
	// do we have any units in the control list and a valid interaction location under the cursor?
	if (ControlledUnits.Num() > 0 && GetLocationUnderCursor(CachedInteraction))
	{
		// move the selected units to the target location
		DoMoveUnitsCommand();
	}
}
#pragma endregion mouse

#pragma region camera
void AWuXiaPlayerController::MoveCamera(const FInputActionValue& Value)
{
	FVector2D InputVector = Value.Get<FVector2D>();

	// get the forward input component vector
	FRotator ForwardRot = GetControlRotation();
	ForwardRot.Pitch = 0.0f;

	// get the right input component vector
	FRotator RightRot = GetControlRotation();
	ForwardRot.Pitch = 0.0f;
	ForwardRot.Roll = 0.0f;

	// add the forward input
	ControlledPawn->AddMovementInput(ForwardRot.RotateVector(FVector::ForwardVector), InputVector.X + InputVector.Y);

	// add the right input
	ControlledPawn->AddMovementInput(RightRot.RotateVector(FVector::RightVector), InputVector.X - InputVector.Y);
}

void AWuXiaPlayerController::ZoomCamera(const FInputActionValue& Value)
{
	// scale the input and subtract from the current zoom level
	float ZoomLevel = CameraZoom - (Value.Get<float>() * ZoomScaling);

	// clamp to min/max zoom levels
	CameraZoom = FMath::Clamp(ZoomLevel, MinZoomLevel, MaxZoomLevel);

	// update the pawn's camera
	ControlledPawn->SetZoomModifier(CameraZoom);
}

void AWuXiaPlayerController::ResetCamera(const FInputActionValue& Value)
{
	// reset zoom level to its initial value
	CameraZoom = DefaultZoom;

	// update the pawn's camera
	ControlledPawn->SetZoomModifier(DefaultZoom);
}

#pragma endregion	

const TArray<AWX_PlayerCharacterUnit*>& AWuXiaPlayerController::GetSelectedUnits()
{
	return ControlledUnits;
}

void AWuXiaPlayerController::ResetInteraction()
{
	bAllowInteraction=true;
}

void AWuXiaPlayerController::DoSelectionCommand()
{
	if (GetLocationUnderCursor(CachedSelection))
	{
		FHitResult OutHit;

		const FVector Start = CachedSelection;
		const FVector End = Start + FVector::UpVector * 350.0f;
		FCollisionShape InteractionSphere;
		InteractionSphere.SetSphere(InteractionRadius);

		FCollisionObjectQueryParams ObjectParams;
		ObjectParams.AddObjectTypesToQuery(ECC_Pawn);
		
		FCollisionQueryParams QueryParams;
		QueryParams.AddIgnoredActor(this);
		QueryParams.AddIgnoredActor(GetPawn());
		QueryParams.bTraceComplex = true;

		GetWorld()->SweepSingleByObjectType(OutHit, Start, End, FQuat::Identity, ObjectParams, InteractionSphere, QueryParams);

		if (bSelectedDebug)
		{
			DrawDebugSphere(
			GetWorld(),
			CachedSelection,
			InteractionRadius,
			32,
			FColor::Green,
			false,
			2.0f,
			0,
			2.0f
		);
		}
		

		// if we're using the mouse and are not holding the selection modifier key, deselect any units first
		if (!bSelectionModifier)
		{
			// deselect all units
			DoDeselectAllCommand();
		}

		if (OutHit.bBlockingHit)
		{
			TargetUnit=Cast<AWX_PlayerCharacterUnit>(OutHit.GetActor());
			if (TargetUnit->CharacterState==ECharacterState::Exploration)
			{
				if (TargetUnit)
				{
					if (ControlledUnits.Contains(TargetUnit))
					{
						// remove the units from the controlled list
						ControlledUnits.Remove(TargetUnit);
						// tell the unit it's been deselected
						TargetUnit->UnitDeselected();
					}
					else
					{
						// add the unit to the controlled list
						ControlledUnits.Add(TargetUnit);
						// tell the unit it's been selected
						TargetUnit->UnitSelected();
					
						FollowSystemComponentClass->SetTeamLeader(TargetUnit);
					}
				}
			}
				
		}
	}
}

void AWuXiaPlayerController::DoDeselectAllCommand()
{
	// tell each controlled unit it's been deselected
	for (AWX_PlayerCharacterUnit* CurrentUnit : ControlledUnits)
	{
		// ensure the unit hasn't been destroyed
		if (IsValid(CurrentUnit))
		{
			if (CurrentUnit->CharacterState==ECharacterState::Exploration)
			{
				CurrentUnit->UnitDeselected();
			}
			else
			{
				bExplorationMode=false;
			}
		}
	}

	if (bExplorationMode)
	{
		// clear the controlled units list
		ControlledUnits.Empty();
	}
	
}

void AWuXiaPlayerController::DoMoveUnitsCommand()
{
	// this will be set to true if any of the move requests fail
	bool bInteractionFailed = false;
	
	if (TargetUnit->CharacterState==ECharacterState::Exploration)
	{
		FollowSystemComponentClass->UpdateFollowPositions();
		// set the movement goal
		FVector CurrentMoveGoal;
	
		// set the cached selection as our move goal
		CurrentMoveGoal = CachedInteraction;
	
		// get the closest selected unit to the move goal. This will be our lead unit
		AWX_PlayerCharacterUnit* Closest = GetClosestSelectedUnitToLocation(CurrentMoveGoal);

		// process each unit in the controlled list
		for (AWX_PlayerCharacterUnit* CurrentUnit : ControlledUnits)
		{
			if (IsValid(CurrentUnit))
			{

				// stop the unit
				CurrentUnit->StopMoving();

				// move the lead unit to the goal, all other units to random navigable points around it
				FVector MoveGoal = CurrentMoveGoal;

				if (CurrentUnit != Closest)
				{

					UNavigationSystemV1::K2_GetRandomLocationInNavigableRadius(GetWorld(), CurrentMoveGoal, MoveGoal, InteractionRadius * 0.66f);
				}

				// subscribe to the unit's move completed delegate
				CurrentUnit->OnMoveCompleted.AddDynamic(this, &AWX_CharacterController::OnMoveCompleted);

				// set up movement to the goal location
			
				if (!CurrentUnit->MoveToLocation(MoveGoal, InteractionRadius * 0.66f))
				{
					// the move request failed, so flag it
					bInteractionFailed = true;
				}
			}
		}

		// play the cursor feedback depending on whether our move succeeded or not
		BP_CursorFeedback(CachedInteraction, !bInteractionFailed);
	}
	else if (TargetUnit->CharacterState==ECharacterState::Combat)
	{
		// subscribe to the unit's move completed delegate
		TargetUnit->OnMoveCompleted.AddDynamic(this, &AWX_CharacterController::OnMoveCompleted);

		// set up movement to the goal location
		if (TargetUnit->GetActorLocation()==TargetUnit->CanReachTargetLocation)
		{
			GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Red, TEXT("TargetUnit is already at the target location!"));
		}
			
		if (TargetUnit->MoveToLocation(TargetUnit->CanReachTargetLocation, InteractionRadius * 0.66f))
		{
			TargetUnit->CurrentActionPoints-=TargetUnit->MaxReachableActionPoints;
		}
	}
	
}

AWX_PlayerCharacterUnit* AWuXiaPlayerController::GetClosestSelectedUnitToLocation(FVector TargetLocation)
{
	// closest unit and distance
	AWX_PlayerCharacterUnit* OutUnit = nullptr;
	float Closest = 0.0f;

	// process each unit on the list
	for (AWX_PlayerCharacterUnit* CurrentUnit : ControlledUnits)
	{
		if (CurrentUnit != nullptr)
		{
			// have we selected a unit already?
			if (OutUnit != nullptr)
			{
				// calculate the squared distance to the target location
				float Dist = FVector::DistSquared2D(TargetLocation, CurrentUnit->GetActorLocation());

				// is this unit closer?
				if (Dist < Closest)
				{
					// update the closest unit and distance
					OutUnit = CurrentUnit;
					Closest = Dist;
				}

			} else {

				// no previously selected unit, so use this one
				OutUnit = CurrentUnit;

				// initialize the closest distance
				Closest = FVector::DistSquared2D(TargetLocation, CurrentUnit->GetActorLocation());
			}
		}
		
	}

	// return the selected unit
	return OutUnit;
}

void AWuXiaPlayerController::OnMoveCompleted(AWX_PlayerCharacterUnit* MovedUnit)
{
	// is the unit valid?
	if (IsValid(MovedUnit))
	{
		// unsubscribe from the delegate
		MovedUnit->OnMoveCompleted.RemoveDynamic(this, &AWuXiaPlayerController::OnMoveCompleted);
		
		// skip if interactions are locked
		if (!bAllowInteraction)
		{
			return;
		}

		// disallow additional interactions until we reset
		bAllowInteraction = false;

		// is the unit close enough to the cached interaction location?
		if(FVector::Dist2D(CachedInteraction, MovedUnit->GetActorLocation()) < InteractionRadius)
		{

			// do an overlap test to find nearby interactive objects
			TArray<FOverlapResult> OutOverlaps;

			FCollisionShape CollisionSphere;
			CollisionSphere.SetSphere(InteractionRadius);

			FCollisionObjectQueryParams ObjectParams;
			ObjectParams.AddObjectTypesToQuery(ECC_WorldDynamic);
			
			FCollisionQueryParams QueryParams;

			QueryParams.AddIgnoredActor(MovedUnit);

			for(const AWX_PlayerCharacterUnit* CurSelected : ControlledUnits)
			{
				QueryParams.AddIgnoredActor(CurSelected);
			}

			if (GetWorld()->OverlapMultiByObjectType(OutOverlaps, CachedInteraction, FQuat::Identity, ObjectParams, CollisionSphere, QueryParams))
			{
				for (const FOverlapResult& CurrentOverlap : OutOverlaps)
				{
					if (AWX_PlayerCharacterUnit* CurrentUnit = Cast<AWX_PlayerCharacterUnit>(CurrentOverlap.GetActor()))
					{
						CurrentUnit->Interact(MovedUnit);
					}
				}
			}
		}
	}
}

void AWuXiaPlayerController::DragSelectUnits(const TArray<AWX_PlayerCharacterUnit*>& Units)
{
	// do we have units in the list?
	if (Units.Num() > 0)
	{
		// ensure any previous units are deselected
		DoDeselectAllCommand();

		// select each new unit
		for (AWX_PlayerCharacterUnit* CurrentUnit : Units)
		{
			// add the unit to the selection list
			ControlledUnits.Add(CurrentUnit);

			// select the unit
			CurrentUnit->UnitSelected();
		}
	}
}
