#pragma once

#include "CoreMinimal.h"
#include "WX_CharacterAttribute.generated.h"


USTRUCT(BlueprintType)
struct WUXIA_API FWX_CharacterAttribute
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	TSubclassOf<UTexture2D> CharacterIcon;
	//生命
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Health;

	//内力
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Stamina;

	//经验
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	int32 Experience;

	//行动力
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	int32 ActionPoints;

	//悟性
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	int32 Insight;

	//道德
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	int32 Morality;

	//名声
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	int32 Fame;

	//臂力
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Strength;

	//内劲
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Agility;

	//体魄
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Intelligence;

	//身法
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Dexterity;

	//灵巧
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Charisma;

	//意志
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Willpower;

	FWX_CharacterAttribute()
		: Health(100.0f)
		, Stamina(100.0f)
		, ActionPoints(10.0f)
		, Strength(10.0f)
		, Agility(10.0f)
		, Intelligence(10.0f)
		, Dexterity(10.0f)
		, Charisma(10.0f)
		, Willpower(10.0f)
	{
	}
};

//角色类型
UENUM()
enum class ECharacterType:uint8
{
	Enemy,
	Player,
	NPC,
};

//角色状态
UENUM(BlueprintType)
enum class ECharacterState : uint8
{
	Exploration UMETA(DisplayName = "Exploration"),
	Combat UMETA(DisplayName = "Combat"),
};

//角色队伍类型
UENUM()
enum class EPlayerChacterTeamType: uint8
{
	Team UMETA(DisplayName = "Team"),
	//分离
	Separate UMETA(DisplayName = "Separate"),
};

//攻击距离
UENUM()
enum class EAttackRangeType:uint8
{
	Melee UMETA(DisplayName = "近战"),
	Ranged UMETA(DisplayName = "远程"),
};

//攻击目标类型
UENUM()
enum class EAttackTargetType : uint8
{
	Single UMETA(DisplayName = "单体"),
	AOE_CIRCLE UMETA(DisplayName = "范围-圆形"),
	AOE_LINE UMETA(DisplayName = "范围-直线"),
	AOE_CONE UMETA(DisplayName = "范围-扇形"),
	SELF UMETA(DisplayName = "自身/光环"),
};

//技能属性系别
UENUM()
enum class ESkillAttributeType : uint8
{
	NEUTRAL	UMETA(DisplayName = "无属性"),
	FIRE UMETA(DisplayName = "火系"),
	WATER UMETA(DisplayName = "水/冰系"),
	WOOD UMETA(DisplayName = "木/毒"),
	METAL UMETA(DisplayName = "金/锋锐"),
	EARTH UMETA(DisplayName = "土"),
	INNER UMETA(DisplayName = "内劲"),
};

//技能类型
UENUM()
enum class ESkillCategory : uint8
{
	BASIC_ATTACK UMETA(DisplayName = "基础攻击"),
	INTERNAL UMETA(DisplayName = "内功/状态"),
	LIGHTNESS_SKILL UMETA(DisplayName = "身法"),
	STRIKE UMETA(DisplayName = "拳脚"),
	SWORD UMETA(DisplayName = "剑法"),
	SABER UMETA(DisplayName = "刀法"),
	STAFF UMETA(DisplayName = "棍法"),
	HIDDEN_WEAPON UMETA(DisplayName = "暗器"),
};

//技能作用效果
UENUM()
enum class ESkillEffectType : uint8
{
	DAMAGE UMETA(DisplayName = "伤害"),
	HEAL UMETA(DisplayName = "治疗"),
	BUFF UMETA(DisplayName = "增益"),
	DEBUFF UMETA(DisplayName = "减益/削弱"),
	CONTROL UMETA(DisplayName = "控制/封穴/击退"),
};

//技能施放方式
UENUM()
enum class ESkillCastType : uint8
{
	INSTANT UMETA(DisplayName = "瞬发"),
	CHARGE UMETA(DisplayName = "蓄力"),
	CHARGED UMETA(DisplayName = "蓄力后释放"),
	PASSIVE UMETA(DisplayName = "被动"),
};

//技能冷却类型
UENUM()
enum class ECooldownType : uint8
{
	NO_COOLDOWN UMETA(DisplayName = "无冷却"),
	COOLDOWN UMETA(DisplayName = "单独冷却"),
	RESOURCE_BASED UMETA(DisplayName = "基于内力/体力消耗"),
	SHARED_COOLDOWN UMETA(DisplayName = "共享冷却"),
};

//行动力消耗类型
UENUM()
enum class EActionCostType: uint8
{
	NO_COST UMETA(DisplayName = "无消耗"),
	INNER_FORCE UMETA(DisplayName = "内力消耗"),
	STAMINA UMETA(DisplayName = "体力消耗"),
	ACTION_POINTS UMETA(DisplayName = "行动力消耗"),
	HP UMETA(DisplayName = "生命值消耗"),
	ITEM UMETA(DisplayName = "消耗道具/暗器数"),
};
