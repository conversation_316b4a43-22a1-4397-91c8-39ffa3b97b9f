

#pragma once

#include "CoreMinimal.h"
#include "Interface/BPI_CharacterInterface.h"
#include "WuXia/WuXiaPlayerController.h"
#include "WX_CharacterController.generated.h"

class AWX_BattleMag;
class UWX_FollowSystemComponent;
//每次角色运行完自己回合需要发消息
DECLARE_DYNAMIC_DELEGATE_OneParam(FONNextTurnDelegate, AWX_PlayerCharacterUnit*, CurrentUnit);
/**
 * 
 */
UCLASS()
class WUXIA_API AWX_CharacterController : public AWuXiaPlayerController,public IBPI_CharacterInterface
{
	GENERATED_BODY()

	AWX_CharacterController();

	virtual void BeginPlay() override;	
	
public:

	virtual void UnitSelected_Implementation() override;

	FONNextTurnDelegate ONNextTurn;

#pragma region 战斗相关

private:
	//战斗当前角色
	AWX_PlayerCharacterUnit* BattleCurrentUnit;
	
public:
	
	//获取当前角色
	UFUNCTION(BlueprintCallable, Category = "Character")
	AWX_PlayerCharacterUnit* GetCurrentCharacter() const { return BattleCurrentUnit; }

	
#pragma endregion 	
};
