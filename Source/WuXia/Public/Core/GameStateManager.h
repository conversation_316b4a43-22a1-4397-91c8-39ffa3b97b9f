// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "../WuXiaGameMode.h"
#include "GameFramework/GameModeBase.h"
#include "Engine/Engine.h"
#include "GameStateManager.generated.h"



/**
 * 
 */ 
UCLASS()
class WUXIA_API AGameStateManager : public AWuXiaGameMode
{
	GENERATED_BODY()

public:
	AGameStateManager();

protected:
	virtual void BeginPlay() override;

	
public:

	UFUNCTION(BlueprintCallable, Category = "Combat")
	class AWX_PlayerCharacterUnit* GetCurrentTurnCharacter() const;

	// 当前玩家角色
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Combat")
	TSoftClassPtr<AWX_PlayerCharacterUnit> CurrentPlayer;
	
protected:

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = "Combat")
	TArray<class AWX_PlayerCharacterUnit*> AllCombatCharacters;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = "Combat")
	TArray<class AWX_PlayerCharacterUnit*> PlayerCharacters;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = "Combat")
	TArray<class AWX_PlayerCharacterUnit*> EnemyCharacters;
	
	//当前回合角色顺序索引
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Combat")
	int32 CurrentTurnIndex;

	// 是否在战斗逻辑
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Combat")
	bool bIsCombatActive;

private:
	// 进入战斗后，初始化所有进入战斗的角色，包括敌方,并按照先手进行排序
	void InitializeCombatCharacters();
};
