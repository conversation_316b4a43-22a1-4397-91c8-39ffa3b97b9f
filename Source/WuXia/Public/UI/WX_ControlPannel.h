// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Event/GlobalEventMag.h"
#include "WX_ControlPannel.generated.h"

/**
 * 
 */
UCLASS()
class WUXIA_API UWX_ControlPannel : public UUserWidget
{
	GENERATED_BODY()

public:
	UFUNCTION()
	void OnActionButtonClicked();
	UFUNCTION()
	virtual void NativeConstruct() override;

	UPROPERTY()
	UWX_EventPayload* Payload;
	UPROPERTY()
	UWX_EventSystem* EventSystem;
	
	//结束按钮
	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	class UButton* ActionBtn;

	//回城按钮
	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	class UButton* ReturnBtn;
};
