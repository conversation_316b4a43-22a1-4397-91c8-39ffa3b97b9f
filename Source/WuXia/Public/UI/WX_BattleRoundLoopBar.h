// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "WX_BattleRoundLoopBar.generated.h"

class UWX_BattleCharacterIcon;
class UWX_EventSystem;
class UWX_EventPayload;
/**
 * 
 */
UCLASS()
class WUXIA_API UWX_BattleRoundLoopBar : public UUserWidget
{
	GENERATED_BODY()

	
	virtual void NativeConstruct() override;
	
protected:
	
	UPROPERTY()
	UWX_EventPayload* Payload;
	UPROPERTY()
	UWX_EventSystem* EventSystem;
	
public:
	// Overlay for the round loop bar
	UPROPERTY(meta = (BindWidget))
	class UOverlay* RoundLoopBarOverlay;
	// Horizontal box for the round loop bar
	UPROPERTY(meta = (BindWidget))
	class UHorizontalBox* RoundLoopBarBox;

	UFUNCTION(BlueprintCallable, Category = "Round Loop Bar")
	void SetCharacterList(UWX_EventPayload* Uwx_EventPayload);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Character")
	TSubclassOf<UWX_BattleCharacterIcon>  CharacterIconClass;

	
};
