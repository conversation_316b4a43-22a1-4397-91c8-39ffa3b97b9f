// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "WX_BattleRoundLoopBar.generated.h"

/**
 * 
 */
UCLASS()
class WUXIA_API UWX_BattleRoundLoopBar : public UUserWidget
{
	GENERATED_BODY()

public:
	// Overlay for the round loop bar
	UPROPERTY(meta = (BindWidget))
	class UOverlay* RoundLoopBarOverlay;
	// Horizontal box for the round loop bar
	UPROPERTY(meta = (BindWidget))
	class UHorizontalBox* RoundLoopBarBox;

	UFUNCTION(BlueprintCallable, Category = "Round Loop Bar")
	void AddCharacterIcon(TSubclassOf<UTexture2D> CharacterIcon);
	
};
