// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "WX_BattleCharacterIcon.generated.h"

/**
 * 
 */
UCLASS()
class WUXIA_API UWX_BattleCharacterIcon : public UUserWidget
{
	GENERATED_BODY()

	virtual void NativeConstruct() override;
public:
	//overlay
	UPROPERTY(meta = (BindWidget))
	class UOverlay* CharacterIconOverlay;
	//horizontal box
	UPROPERTY(meta = (BindWidget))
	class UHorizontalBox* CharacterIconBox;
	//character icon
	UPROPERTY(meta = (BindWidget))
	class UImage* CharacterIconImage;
	//spacebar
	UPROPERTY(meta = (BindWidget))
	class USpacer* SpaceBarSpacer;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Character")
	TSubclassOf<UTexture2D>  CharacterIcon;
	
	
	
};
