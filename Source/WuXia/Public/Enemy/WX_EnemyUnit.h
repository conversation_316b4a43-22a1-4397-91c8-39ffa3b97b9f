// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Character/WX_CharacterUnitBase.h"
#include "CharacterStruct/WX_CharacterAttribute.h"
#include "WX_EnemyUnit.generated.h"

UCLASS()
class WUXIA_API AWX_EnemyUnit : public AWX_CharacterUnitBase
{
	GENERATED_BODY()

public:
	// Sets default values for this character's properties
	AWX_EnemyUnit();

protected:
	
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;


public:

	// Called to bind functionality to input
	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

	// AI相关方法

	// 检查是否可以执行攻击
	UFUNCTION(BlueprintCallable, Category = "AI Combat")
	bool CanPerformAttack(class AWX_CharacterUnitBase* Target, int32 ActionPointCost = 1);

	// 执行攻击动作
	UFUNCTION(BlueprintCallable, Category = "AI Combat")
	void PerformAttackAction(class AWX_CharacterUnitBase* Target);

	// 检查能到达的最远位置
	UFUNCTION(BlueprintCallable, Category = "AI Movement")
	void CanReachMaxLocation(const FVector& TargetPosition);

	// 获取到指定位置的最佳路径
	UFUNCTION(BlueprintCallable, Category = "AI Movement")
	TArray<FVector> GetPathToPosition(const FVector& TargetPosition);

	// 消耗行动点
	UFUNCTION(BlueprintCallable, Category = "AI")
	bool ConsumeActionPoints(int32 Points);

	// 检查是否有足够的行动点
	UFUNCTION(BlueprintCallable, Category = "AI")
	bool HasEnoughActionPoints(int32 RequiredPoints) const;

	// 获取当前状态信息（用于AI决策）
	UFUNCTION(BlueprintCallable, Category = "AI")
	FString GetStatusInfo() const;
};
