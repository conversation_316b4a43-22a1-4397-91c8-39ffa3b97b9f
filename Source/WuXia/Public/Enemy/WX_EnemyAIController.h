// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "CharacterStruct/WX_CharacterAttribute.h"
#include "Event/WX_EventSystem.h"
#include "Perception/AIPerceptionTypes.h"
#include "WX_EnemyAIController.generated.h"

UCLASS()
class WUXIA_API AWX_EnemyAIController : public AAIController
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AWX_EnemyAIController();
	
	
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	class UAIPerceptionComponent* EnemyPerceptionComponent;
    
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	class UAISenseConfig_Sight* SightConfig;

protected:
	
	virtual void OnPossess(APawn* InPawn) override;
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	UPROPERTY()
	UWX_EventPayload* Payload;
	UPROPERTY()
	UWX_EventSystem* EventSystem;

	FWX_CharacterAttribute CharacterAttribute;
	
	UPROPERTY()
	int32 CurrentActionPoints=CharacterAttribute.ActionPoints;

	
	
public:

	UFUNCTION()
	void OnEnemyPerceptionUpdated(AActor* Actor,FAIStimulus stimulus);


	//初始化AI
	UFUNCTION()
	void InitializeAI();

	//行为树
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	class UBehaviorTree* BehaviorTree;

	//黑板
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
	class UBlackboardComponent* BlackboardComp;

	// 行为树组件
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
	class UBehaviorTreeComponent* BehaviorTreeComp;

	// 启动行为树
	UFUNCTION(BlueprintCallable, Category = "AI")
	void StartBehaviorTree();

	// 停止行为树
	UFUNCTION(BlueprintCallable, Category = "AI")
	void StopBehaviorTree();

	// 更新黑板数据
	UFUNCTION(BlueprintCallable, Category = "AI")
	void UpdateBlackboardData();

	// 获取最近的敌人
	UFUNCTION(BlueprintCallable, Category = "AI")
	AWX_CharacterUnitBase* GetNearestEnemy();

	// 获取最佳攻击位置
	UFUNCTION(BlueprintCallable, Category = "AI")
	FVector GetBestAttackPosition(AWX_CharacterUnitBase* Target);

	// 检查是否可以攻击目标
	UFUNCTION(BlueprintCallable, Category = "AI")
	bool CanAttackTarget(AWX_CharacterUnitBase* Target);

	// 检查是否有足够行动点移动到位置
	UFUNCTION(BlueprintCallable, Category = "AI")
	bool HasEnoughActionPointsToMove(const FVector& TargetLocation);

	// 获取当前拥有的角色单位
	UFUNCTION(BlueprintCallable, Category = "AI")
	AWX_EnemyUnit* GetControlledEnemyUnit();
	

};
