// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "BTTask_WX_AttackTarget.generated.h"

/**
 * 自定义行为树任务：攻击目标
 * 执行攻击动作并处理攻击结果
 */
UCLASS()
class WUXIA_API UBTTask_WX_AttackTarget : public UBTTaskNode
{
	GENERATED_BODY()

public:
	UBTTask_WX_AttackTarget();

	virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
	virtual FString GetStaticDescription() const override;

protected:
	// 目标对象的黑板键
	UPROPERTY(EditAnywhere, Category = "Blackboard")
	struct FBlackboardKeySelector TargetActorKey;

	// 攻击类型
	UPROPERTY(EditAnywhere, Category = "Attack")
	bool bIsMeleeAttack = true;

	// 攻击范围
	UPROPERTY(EditAnywhere, Category = "Attack")
	float AttackRange = 200.0f;

	// 攻击消耗的行动点
	UPROPERTY(EditAnywhere, Category = "Attack")
	int32 AttackActionPointCost = 1;

private:
	// 检查是否可以攻击目标
	bool CanAttackTarget(class AWX_EnemyUnit* Attacker, class AWX_CharacterUnitBase* Target);
	
	// 执行攻击
	void PerformAttack(class AWX_EnemyUnit* Attacker, class AWX_CharacterUnitBase* Target);
};
