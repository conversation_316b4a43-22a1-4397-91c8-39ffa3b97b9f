// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTService.h"
#include "BTService_WX_UpdateBlackboard.generated.h"

/**
 * 自定义行为树服务：更新黑板数据
 * 定期更新AI需要的关键信息
 */
UCLASS()
class WUXIA_API UBTService_WX_UpdateBlackboard : public UBTService
{
	GENERATED_BODY()

public:
	UBTService_WX_UpdateBlackboard();

protected:
	virtual void TickNode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds) override;
	virtual FString GetStaticDescription() const override;

	// 是否更新最近敌人信息
	UPROPERTY(EditAnywhere, Category = "Update Settings")
	bool bUpdateNearestEnemy = true;

	// 是否更新自身状态信息
	UPROPERTY(EditAnywhere, Category = "Update Settings")
	bool bUpdateSelfStatus = true;

	// 是否更新战场信息
	UPROPERTY(EditAnywhere, Category = "Update Settings")
	bool bUpdateBattlefieldInfo = true;

	// 最近敌人的黑板键
	UPROPERTY(EditAnywhere, Category = "Blackboard", meta = (EditCondition = "bUpdateNearestEnemy"))
	struct FBlackboardKeySelector NearestEnemyKey;

	// 目标位置的黑板键
	UPROPERTY(EditAnywhere, Category = "Blackboard", meta = (EditCondition = "bUpdateNearestEnemy"))
	struct FBlackboardKeySelector TargetLocationKey;

	// 到目标距离的黑板键
	UPROPERTY(EditAnywhere, Category = "Blackboard", meta = (EditCondition = "bUpdateNearestEnemy"))
	struct FBlackboardKeySelector DistanceToTargetKey;

	// 当前行动点的黑板键
	UPROPERTY(EditAnywhere, Category = "Blackboard", meta = (EditCondition = "bUpdateSelfStatus"))
	struct FBlackboardKeySelector CurrentActionPointsKey;

	// 自身位置的黑板键
	UPROPERTY(EditAnywhere, Category = "Blackboard", meta = (EditCondition = "bUpdateSelfStatus"))
	struct FBlackboardKeySelector SelfLocationKey;

private:
	// 更新最近敌人信息
	void UpdateNearestEnemyInfo(class AWX_EnemyAIController* AIController, class UBlackboardComponent* BlackboardComp);
	
	// 更新自身状态信息
	void UpdateSelfStatusInfo(class AWX_EnemyAIController* AIController, class UBlackboardComponent* BlackboardComp);
};
