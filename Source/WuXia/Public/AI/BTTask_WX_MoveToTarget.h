// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "Navigation/PathFollowingComponent.h"
#include "BTTask_WX_MoveToTarget.generated.h"

class AWX_EnemyAIController;
/**
 * 自定义行为树任务：移动到目标位置
 * 考虑行动点消耗和战术位置选择
 */
UCLASS()
class WUXIA_API UBTTask_WX_MoveToTarget : public UBTTaskNode
{
	GENERATED_BODY()

public:
	UBTTask_WX_MoveToTarget();


	void OnMoveFinished(FAIRequestID FaiRequestID, const FPathFollowingResult& PathFollowingResult);
	
	virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
	virtual void OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult) override;
	virtual FString GetStaticDescription() const override;

protected:
	// 目标对象的黑板键
	UPROPERTY(EditAnywhere, Category = "Blackboard")
	struct FBlackboardKeySelector TargetActorKey;

	// 移动到目标的距离偏移
	UPROPERTY(EditAnywhere, Category = "Movement")
	float AcceptanceRadius = 100.0f;

	// 是否使用最佳攻击位置
	UPROPERTY(EditAnywhere, Category = "Movement")
	bool bUseOptimalAttackPosition = true;

	AWX_EnemyAIController* AIController=nullptr;

private:
	// 计算最佳移动位置
	FVector CalculateOptimalMovePosition(class AWX_CharacterUnitBase* Target);
	
};
