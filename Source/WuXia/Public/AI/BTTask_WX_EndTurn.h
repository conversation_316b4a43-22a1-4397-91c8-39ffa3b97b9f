// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "BTTask_WX_EndTurn.generated.h"

/**
 * 自定义行为树任务：结束回合
 * 当AI完成所有动作或无法继续行动时，结束当前回合
 */
UCLASS()
class WUXIA_API UBTTask_WX_EndTurn : public UBTTaskNode
{
	GENERATED_BODY()

public:
	UBTTask_WX_EndTurn();

	virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
	virtual FString GetStaticDescription() const override;

protected:
	// 是否停止行为树
	UPROPERTY(EditAnywhere, Category = "Turn Management")
	bool bStopBehaviorTree = true;

	// 是否触发下一个角色的回合
	UPROPERTY(EditAnywhere, Category = "Turn Management")
	bool bTriggerNextCharacter = true;

private:
	// 通知战斗管理器回合结束
	void NotifyTurnEnd(class AWX_EnemyAIController* AIController);
};
