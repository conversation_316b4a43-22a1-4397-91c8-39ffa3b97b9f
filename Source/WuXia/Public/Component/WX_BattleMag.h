// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Event/GlobalEventMag.h"
#include "WX_BattleMag.generated.h"


class AWX_CharacterController;
class AWX_CharacterUnitBase;
class AEnterBattleCollision;
class AWX_PlayerCharacterUnit;

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class WUXIA_API AWX_BattleMag : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	AWX_BattleMag();
	
protected:
	// Called when the game starts
	virtual void BeginPlay() override;

	UPROPERTY()
	TSoftObjectPtr<UWX_EventSystem> EventSystem=nullptr;

public:

	TArray<AWX_CharacterUnitBase*> BattlePlayerUnits;
	TArray<AWX_CharacterUnitBase*> BattleEnemyUnits;
	TArray<AWX_CharacterUnitBase*> AllBattleUnits;
	
	UPROPERTY(EditAnywhere, Category = "Battle")
	float BattleDistance = 1000.0f;

	UPROPERTY(EditAnywhere, Category = "Battle")
	float BattleEnterRadius=3000.f;

	UPROPERTY(EditAnywhere, Category = "Battle")
	bool IsEnterDebugSphere=false;

	UPROPERTY()
	AWX_CharacterController* PawnController=nullptr;

#pragma region 战斗循环
	//进入战斗
	UFUNCTION()
	void EnterBattleTurn(UWX_EventPayload* Payload);
	
	//玩家回合
	UFUNCTION()
	void PlayerTurn(AWX_PlayerCharacterUnit* CurrentUnit);

	//enemy回合
	UFUNCTION()
	void AITurn(AWX_EnemyUnit* CurrentUnit);

	//下一个玩家预备
	UFUNCTION()
		void NextCharacterReady(UWX_EventPayload* Payload);
	
	//结束战斗
	UFUNCTION()
	void EndBattleTurn();
#pragma endregion


#pragma region 战斗方法

	UFUNCTION(BlueprintCallable, Category = "Battle")
	void MeleeAttack(AWX_CharacterUnitBase* Attacker, AWX_CharacterUnitBase* Target);

	UFUNCTION(BlueprintCallable, Category = "Battle")
	void RangedAttack(AWX_CharacterUnitBase* Attacker, AWX_CharacterUnitBase* Target);

	
#pragma endregion 
	
	UPROPERTY()
	int CurrentCharacterIndex=0;
	
	//检测距离是否脱战
	void CheckOutBattleProximity(AWX_PlayerCharacterUnit * CurrentUnit);
	//后续加入插入排序
	void InsertBattleUnitSorted(AWX_CharacterUnitBase* CurrentUnit);
	
};
