// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "WuXiaCharacter.generated.h"


class UCameraComponent;
class UFloatingPawnMovement;

UCLASS(Blueprintable)
class AWuXiaCharacter : public APawn
{
	GENERATED_BODY()
	/** Camera */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components", meta = (AllowPrivateAccess = "true"))
	UCameraComponent* Camera;

	/** Movement Component */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera", meta = (AllowPrivateAccess = "true"))
	UFloatingPawnMovement* FloatingPawnMovement;
	
public:
	AWuXiaCharacter();



	/** Sets the camera zoom modifier value */
	void SetZoomModifier(float Value);
	
	/** Returns the camera component */
	UCameraComponent* GetCamera() const { return Camera; }
};

