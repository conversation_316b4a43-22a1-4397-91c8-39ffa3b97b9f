// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class WuXia : ModuleRules
{
	public WuXia(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(new string[] { "Core", "CoreUObject", "Engine", "InputCore", 
	        "NavigationSystem", "AIModule", "Niagara", "EnhancedInput","EditorSubsystem","GameplayAbilities","UMG","GameplayTags" });
    }
}
