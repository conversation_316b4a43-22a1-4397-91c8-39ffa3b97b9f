#include "Character/WX_PlayerCharacterUnit.h"
#include "NavigationSystem.h"
#include "AbilitySystemComponent.h"
#include "Character/WX_CharacterController.h"
#include "Components/SphereComponent.h"
#include "Components/SplineComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "NavFilters/NavigationQueryFilter.h"
#include "Navigation/PathFollowingComponent.h"


void AWX_PlayerCharacterUnit::NotifyControllerChanged()
{
	// validate and save a copy of the AI controller reference
	AIController = Cast<AAIController>(Controller);
	
	if (AIController)
	{
		// subscribe to the move finished handler on the path following component
		UPathFollowingComponent* PFComp = AIController->GetPathFollowingComponent();
		if (PFComp)
		{
			PFComp->OnRequestFinished.AddUObject(this, &AWX_PlayerCharacterUnit::OnMoveFinished);
		}
	}
}

// Sets default values
AWX_PlayerCharacterUnit::AWX_PlayerCharacterUnit()
{
	// Set this character to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	MovementPreviewSpline= CreateDefaultSubobject<USplineComponent>(TEXT("MovementPreviewSpline"));
	MovementPreviewSpline->SetupAttachment(RootComponent);
	MovementPreviewSpline->SetVisibility(false);

	AbilitySystemComponent=CreateDefaultSubobject<UAbilitySystemComponent>(TEXT("AbilitySystemComp"));

	InteractionRange = CreateDefaultSubobject<USphereComponent>(TEXT("Interaction Range"));
	InteractionRange->SetupAttachment(RootComponent);

	InteractionRange->SetSphereRadius(100.0f);
	InteractionRange->SetCollisionProfileName(FName("OverlapAllDynamic"));

	// configure movement
	GetCharacterMovement()->GravityScale = 1.5f;
	GetCharacterMovement()->MaxAcceleration = 1000.0f;
	GetCharacterMovement()->BrakingFrictionFactor = 1.0f;
	GetCharacterMovement()->BrakingDecelerationWalking = 1000.0f;
	GetCharacterMovement()->PerchRadiusThreshold = 20.0f;
	GetCharacterMovement()->bUseFlatBaseForFloorChecks = true;
	GetCharacterMovement()->RotationRate = FRotator(0.0f, 640.0f, 0.0f);
	GetCharacterMovement()->bOrientRotationToMovement = true;
	GetCharacterMovement()->AvoidanceConsiderationRadius = 150.0f;
	GetCharacterMovement()->AvoidanceWeight = 1.0f;
	GetCharacterMovement()->bConstrainToPlane = true;
	GetCharacterMovement()->bSnapToPlaneAtStart = true;
	GetCharacterMovement()->SetFixedBrakingDistance(200.0f);
	GetCharacterMovement()->SetFixedBrakingDistance(true);

}

void AWX_PlayerCharacterUnit::BeginPlay()
{
	Super::BeginPlay();
	PlayerController = Cast<AWX_CharacterController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));
	OnBeginCursorOver.AddUniqueDynamic(this, &AWX_PlayerCharacterUnit::OnCursorBegin);
	OnEndCursorOver.AddUniqueDynamic(this, &AWX_PlayerCharacterUnit::OnCursorEnd);
}

// Called every frame
void AWX_PlayerCharacterUnit::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	if (CharacterState==ECharacterState::Combat&&IsBeenSelected)
	{
		
		// AWX_PlayerCharacterUnit * Character = Cast<AWX_PlayerCharacterUnit>(PlayerController->GetPawn());PlayerController-
		FVector MouseWorldLocation;
		PlayerController->GetLocationUnderCursor(MouseWorldLocation);
		UpdatePreviewPath(MouseWorldLocation);
		DrawPreviewPath();
	}
}

void AWX_PlayerCharacterUnit::UpdatePreviewPath(const FVector& MouseWorldLocation)
{
	FVector StartLocation = GetActorLocation();
	PathPoints = FindPath(StartLocation, MouseWorldLocation);

	if (PathPoints.Num() > 1)
	{
		MovementPreviewSpline->ClearSplinePoints();
		for (int32 i = 0; i < PathPoints.Num(); ++i)
		{
			MovementPreviewSpline->AddSplinePoint(PathPoints[i], ESplineCoordinateSpace::World);
		}
		MovementPreviewSpline->UpdateSpline();
	}
}

void AWX_PlayerCharacterUnit::ClearPreviewPath()
{
	MovementPreviewSpline->ClearSplinePoints();
	PathPoints.Empty();
}

void AWX_PlayerCharacterUnit::DrawPreviewPath()
{
	float CumulativeDistance = 0.0f;
	
	MaxReachableActionPoints =GetMaxReachableActionPoints(PathPoints);
	CanReachTargetLocation= PathPoints.IsValidIndex(MaxReachableActionPoints) ? PathPoints[MaxReachableActionPoints] : FVector::ZeroVector;
	
	 if (PathPoints.Num() > 1)
        {

	 	// 绘制路径线
	 	for (int32 i = 0; i < PathPoints.Num() - 1; ++i)
	 	{
	 		// 根据是否可到达来选择颜色
	 		FLinearColor LineColor = (i < MaxReachableActionPoints) ? ReachableColor : UnreachableColor;
    
	 		DrawDebugLine(
				 GetWorld(),
				 PathPoints[i] + FVector(0, 0, 10), // 稍微抬高一点避免Z-fighting
				 PathPoints[i + 1] + FVector(0, 0, 10),
				 LineColor.ToFColor(true),
				 false,
				 -1.0f,
				 0,
				 PreviewLineThickness
			 );
	 	}

	 	// 在终点绘制一个圆圈，颜色与最后一个路径段一致
	 	if (PathPoints.Num() > 0)
	 	{
	 		// 确定终点圆圈的颜色（与最后一个点的颜色一致）
	 		FLinearColor EndPointColor = (PathPoints.Num() - 1 <= CharacterAttribute.ActionPoints) ? ReachableColor : UnreachableColor;
    
	 		DrawDebugCircle(
				 GetWorld(),
				 PathPoints.Last(),
				 50.0f,
				 32,
				 EndPointColor.ToFColor(true),
				 false,
				 -1.0f,
				 0,
				 2.0f,
				 FVector(1, 0, 0),
				 FVector(0, 1, 0)
			 );
	 	}
        }
}

bool AWX_PlayerCharacterUnit::GetMouseWorldLocation(FVector& WorldLocation, FVector& WorldDirection)
{
	APlayerController* PC = Cast<APlayerController>(GetController());
	if (!PC)
	{
		return false;
	}
    
	return PC->DeprojectMousePositionToWorld(WorldLocation, WorldDirection);
}

bool AWX_PlayerCharacterUnit::GetGroundLocationUnderCursor(FVector& GroundLocation)
{
	AWX_CharacterController* PC = Cast<AWX_CharacterController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));
	if (!PC)
	{
		return false;
	}
    
	FHitResult HitResult;
	bool bHit = PC->GetHitResultUnderCursor(
		ECollisionChannel::ECC_WorldStatic,
		false,
		HitResult
	);
    
	if (bHit)
	{
		GroundLocation = HitResult.Location;
		return true;
	}
    
	return false;
}



#pragma region 移动
void AWX_PlayerCharacterUnit::StopMoving()
{
	GetCharacterMovement()->StopMovementImmediately();
}

void AWX_PlayerCharacterUnit::UnitSelected()
{
	
	BP_UnitSelected();
}

void AWX_PlayerCharacterUnit::UnitDeselected()
{
	
	BP_UnitDeselected();
}

void AWX_PlayerCharacterUnit::Interact(AWX_PlayerCharacterUnit* Interactor)
{
	// ensure the interactor is valid
	if (IsValid(Interactor))
	{
		// rotate towards the actor we're interacting with
		SetActorRotation(UKismetMathLibrary::FindLookAtRotation(GetActorLocation(), Interactor->GetActorLocation()));

		// signal the interactor to play its interaction behavior
		Interactor->BP_InteractionBehavior(this);

		// play our own interaction behavior
		BP_InteractionBehavior(Interactor);
	}
}

bool AWX_PlayerCharacterUnit::MoveToLocation(const FVector& Location, float AcceptanceRadius)
{
	// ensure we have a valid AI Controller
	if (AIController)
	{
		// set up the AI Move Request
		FAIMoveRequest MoveReq;

		MoveReq.SetGoalLocation(Location);
		MoveReq.SetAcceptanceRadius(AcceptanceRadius);
		MoveReq.SetAllowPartialPath(true);
		MoveReq.SetUsePathfinding(true);
		MoveReq.SetProjectGoalLocation(true);
		MoveReq.SetRequireNavigableEndLocation(true);
		MoveReq.SetNavigationFilter(AIController->GetDefaultNavigationFilterClass());
		MoveReq.SetCanStrafe(false);

		// request a move to the AI Controller
		FNavPathSharedPtr FollowedPath;
		const FPathFollowingRequestResult ResultData = AIController->MoveTo(MoveReq, &FollowedPath);
		
		// check the move result
		switch (ResultData.Code)
		{
			// failed. Return false
		case EPathFollowingRequestResult::Failed:

			return false;
			break;

			// already at goal. Return true and call the move completed delegate
		case EPathFollowingRequestResult::AlreadyAtGoal:

			OnMoveCompleted.Broadcast(this);
			return true;
			break;

			// move successfully scheduled. Return true
		case EPathFollowingRequestResult::RequestSuccessful:

			return true;
			break;
		}
	}

	// the move could not be completed
	return false;
}

void AWX_PlayerCharacterUnit::OnMoveFinished(FAIRequestID RequestID, const FPathFollowingResult& Result)
{
	OnMoveCompleted.Broadcast(this);
}


#pragma endregion	

