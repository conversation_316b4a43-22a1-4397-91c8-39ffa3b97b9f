// Fill out your copyright notice in the Description page of Project Settings.


#include "Character/WX_CharacterUnitBase.h"

#include "NavigationSystem.h"
#include "NavFilters/NavigationQueryFilter.h"


// Sets default values
AWX_CharacterUnitBase::AWX_CharacterUnitBase()
{
	// Set this character to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

// Called when the game starts or when spawned
void AWX_CharacterUnitBase::BeginPlay()
{
	Super::BeginPlay();
	
}

void AWX_CharacterUnitBase::SetSelectedHighLight(bool bSelected)
{
	GetMesh()->SetRenderCustomDepth(bSelected);
	if (bSelected)
	{
		if (CharacterType==ECharacterType::Enemy)
		{
			GetMesh()->SetCustomDepthStencilValue(2);
		}
		else
		{
			GetMesh()->SetCustomDepthStencilValue(1);
		}
		
	}
}

void AWX_CharacterUnitBase::OnCursorBegin(AActor* TouchedActor)
{
	SetSelectedHighLight(true);
}

void AWX_CharacterUnitBase::OnCursorEnd(AActor* TouchedActor)
{
	SetSelectedHighLight(false);
}

TArray<FVector> AWX_CharacterUnitBase::FindPath(const FVector& Start, const FVector& End)
{
	//将FNavPathPoint转换为FVector
	TArray<FVector> TempPathPoints;
    
	if (!GetWorld()) return TempPathPoints;

	// 1. 获取导航系统
	UNavigationSystemV1* NavSys = FNavigationSystem::GetCurrent<UNavigationSystemV1>(GetWorld());
	if (!NavSys) return TempPathPoints;
	
	// 确保导航系统已就绪
	// if (!NavSys->IsNavigationBuilt(GetWorldSettings()))
	// {
	// 	UE_LOG(LogTemp, Warning, TEXT("Navigation system not built yet!"));
	// 	NavSys->Build();
	// }

	ANavigationData* NavData = NavSys->GetDefaultNavDataInstance(FNavigationSystem::DontCreate);
	if (!NavData)
	{
		// 尝试创建导航数据
		NavData = NavSys->GetDefaultNavDataInstance(FNavigationSystem::Create);
        
		if (!NavData) 
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to create NavData!"));
			return TempPathPoints;
		}
	}
	
	// 2. 创建路径查询
	FPathFindingQuery Query;
	Query.StartLocation = Start;
	Query.EndLocation = End;
	Query.NavData = NavData;
	Query.QueryFilter = UNavigationQueryFilter::GetQueryFilter(*NavData, GetWorld(),  UNavigationQueryFilter::StaticClass());

	// 3. 执行同步路径查找
	FPathFindingResult Result = NavSys->FindPathSync(Query);
	
	//Result.IsSuccessful()
	GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, FString::Printf(TEXT("Pathfinding result: %s"), Result.IsSuccessful() ? TEXT("Success") : TEXT("Failure")));
	if (Result.IsSuccessful() && Result.Path.IsValid())
	{
		// 4. 提取路径点
		const FNavigationPath* Path = Result.Path.Get();
		for (const FNavPathPoint& Point : Path->GetPathPoints())
		{
			TempPathPoints.Add(Point.Location);
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Pathfinding failed!"));
	}

	return GenerateDetailedPath(TempPathPoints);
}

TArray<FVector> AWX_CharacterUnitBase::GenerateDetailedPath(const TArray<FVector>& NavMeshPoints)
{
	TArray<FVector> DetailedPath;
    
	if (NavMeshPoints.Num() <= 1)
		return NavMeshPoints;
    
	// 每个行动点对应的距离间隔
	float IntervalDistance = ActionPointToUnitConversion;
    
	DetailedPath.Add(NavMeshPoints[0]); // 添加起点
    
	// 在每两个NavMesh点之间插入细分点
	for (int32 i = 1; i < NavMeshPoints.Num(); i++)
	{
		FVector StartPos = NavMeshPoints[i-1];
		FVector EndPos = NavMeshPoints[i];
		float SegmentLength = FVector::Dist(StartPos, EndPos);
        
		// 计算需要插入多少个中间点
		int32 NumIntervals = FMath::FloorToInt(SegmentLength / IntervalDistance);
        
		if (NumIntervals > 0)
		{
			FVector Direction = (EndPos - StartPos).GetSafeNormal();
            
			// 插入中间点
			for (int32 j = 1; j <= NumIntervals; j++)
			{
				FVector IntermediatePoint = StartPos + Direction * (IntervalDistance * j);
				DetailedPath.Add(IntermediatePoint);
			}
		}
        
		// 添加NavMesh端点
		DetailedPath.Add(EndPos);
	}
    
	return DetailedPath;
}

int32 AWX_CharacterUnitBase::GetMaxReachableActionPoints(TArray<FVector> TempPathPoints)
{
	float CumulativeDistance = 0.0f;
	//计算所需行动力
	for (int32 i = 0; i < TempPathPoints.Num() - 1; ++i)
	{
		float SegmentDistance = FVector::Dist(TempPathPoints[i], TempPathPoints[i+1]);
		CumulativeDistance+= SegmentDistance;
		NeedActionPoints=CalculateActionPointsForDistance(CumulativeDistance);
		GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, FString::Printf(TEXT("NeedActionPoints: %d"), NeedActionPoints));
	}
	
	return  FMath::Min(CurrentActionPoints, NeedActionPoints);
}

// Called every frame
void AWX_CharacterUnitBase::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

// Called to bind functionality to input
void AWX_CharacterUnitBase::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);
}

