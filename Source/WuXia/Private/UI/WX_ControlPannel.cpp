// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/WX_ControlPannel.h"

#include "Components/Button.h"

void UWX_ControlPannel::NativeConstruct()
{
	Super::NativeConstruct();
	EventSystem = GetGameInstance()->GetSubsystem<UWX_EventSystem>();
	ActionBtn->OnClicked.AddUniqueDynamic(this, &UWX_ControlPannel::OnActionButtonClicked);
}

void UWX_ControlPannel::OnActionButtonClicked()
{
	// 创建事件载荷
	FGameplayTag NextCharacterID = FGameplayTag::RequestGameplayTag(FName("Event.Battle.NextCharacter"));
	Payload = EventSystem->CreateEventPayload(EEventType::Combat, NextCharacterID);
	Payload->EventSource = this;
	EventSystem->TriggerEvent(Payload);
}
