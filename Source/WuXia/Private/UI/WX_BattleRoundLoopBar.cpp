// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/WX_BattleRoundLoopBar.h"
#include "GameplayTagContainer.h"
#include "Character/WX_CharacterUnitBase.h"
#include "Component/WX_BattleMag.h"
#include "Components/HorizontalBox.h"
#include "Event/WX_EventSystem.h"
#include "UI/WX_BattleCharacterIcon.h"

void UWX_BattleRoundLoopBar::NativeOnInitialized()
{
	Super::NativeConstruct();
	EventSystem=GetGameInstance()->GetSubsystem<UWX_EventSystem>();
	const FGameplayTag BattleID=FGameplayTag::RequestGameplayTag(FName("Event.Battle.BattleUI"));
	EventSystem->RegisterEventById(BattleID,this,&UWX_BattleRoundLoopBar::SetCharacterList);
}

void UWX_BattleRoundLoopBar::SetCharacterList(UWX_EventPayload* Uwx_EventPayload)
{
	TSoftObjectPtr<AWX_BattleMag> BattleMag=Cast<AWX_BattleMag>(Uwx_EventPayload->EventSource);
	for (auto Character:BattleMag->AllBattleUnits)
	{
		UWX_BattleCharacterIcon* CharacterIcon=CreateWidget<UWX_BattleCharacterIcon>(this,CharacterIconClass);
		CharacterIcon->CharacterIcon=Character->CharacterAttribute.CharacterIcon;
		RoundLoopBarBox->AddChild(CharacterIcon);
	}
}
