// Fill out your copyright notice in the Description page of Project Settings.


#include "Enemy/WX_EnemyUnit.h"
#include "Character/WX_CharacterController.h"
#include "Character/WX_CharacterUnitBase.h"
#include "Component/WX_BattleMag.h"
#include "Kismet/GameplayStatics.h"
#include "Perception/AIPerceptionComponent.h"


// Sets default values
AWX_EnemyUnit::AWX_EnemyUnit()
{
	// Set this character to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}


// Called when the game starts or when spawned
void AWX_EnemyUnit::BeginPlay()
{
	Super::BeginPlay();
	OnBeginCursorOver.AddUniqueDynamic(this, &AWX_EnemyUnit::OnCursorBegin);
	OnEndCursorOver.AddUniqueDynamic(this, &AWX_EnemyUnit::OnCursorEnd);
}

// Called to bind functionality to input
void AWX_EnemyUnit::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);
}

bool AWX_EnemyUnit::CanPerformAttack(AWX_CharacterUnitBase* Target, int32 ActionPointCost)
{
	if (!Target)
		return false;

	// 检查是否有足够的行动点
	if (CurrentActionPoints < ActionPointCost)
		return false;

	// 检查目标是否有效（不是自己，还活着等）
	if (Target == this)
		return false;

	// 检查目标是否在攻击范围内
	float Distance = FVector::Dist(GetActorLocation(), Target->GetActorLocation());
	float AttackRange = 200.0f; // 可以从配置中获取

	return Distance <= AttackRange;
}

void AWX_EnemyUnit::PerformAttackAction(AWX_CharacterUnitBase* Target)
{
	if (!CanPerformAttack(Target))
		return;

	// 面向目标
	FVector Direction = (Target->GetActorLocation() - GetActorLocation()).GetSafeNormal();
	FRotator LookRotation = FRotationMatrix::MakeFromX(Direction).Rotator();
	SetActorRotation(LookRotation);

	// 这里可以播放攻击动画、特效等
	// 实际的伤害计算应该在战斗管理器中处理

	UE_LOG(LogTemp, Log, TEXT("%s 执行攻击动作，目标: %s"), *GetName(), *Target->GetName());
}

void AWX_EnemyUnit::CanReachMaxLocation(const FVector& TargetPosition)
{
	MaxReachableActionPoints = GetMaxReachableActionPoints(GetPathToPosition(TargetPosition));
	CanReachTargetLocation= PathPoints.IsValidIndex(MaxReachableActionPoints) ? PathPoints[MaxReachableActionPoints] : FVector::ZeroVector;
}

TArray<FVector> AWX_EnemyUnit::GetPathToPosition(const FVector& TargetPosition)
{
	return FindPath(GetActorLocation(), TargetPosition);
}

bool AWX_EnemyUnit::ConsumeActionPoints(int32 Points)
{
	if (CurrentActionPoints >= Points)
	{
		CurrentActionPoints -= Points;
		UE_LOG(LogTemp, Log, TEXT("%s 消耗了 %d 行动点，剩余: %d"), *GetName(), Points, CurrentActionPoints);
		return true;
	}

	UE_LOG(LogTemp, Warning, TEXT("%s 行动点不足，需要: %d，当前: %d"), *GetName(), Points, CurrentActionPoints);
	return false;
}

bool AWX_EnemyUnit::HasEnoughActionPoints(int32 RequiredPoints) const
{
	return CurrentActionPoints >= RequiredPoints;
}

FString AWX_EnemyUnit::GetStatusInfo() const
{
	return FString::Printf(TEXT("生命: %.1f, 行动点: %d/%d, 位置: %s"),
		CharacterAttribute.Health,
		CurrentActionPoints,
		CharacterAttribute.ActionPoints,
		*GetActorLocation().ToString());
}

