// Fill out your copyright notice in the Description page of Project Settings.


#include "Enemy/WX_EnemyAIController.h"

#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Character/WX_CharacterController.h"
#include "Character/WX_CharacterUnitBase.h"
#include "Component/WX_BattleMag.h"
#include "Enemy/WX_EnemyUnit.h"
#include "Event/WX_EventSystem.h"
#include "Kismet/GameplayStatics.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISenseConfig_Sight.h"


// Sets default values
AWX_EnemyAIController::AWX_EnemyAIController()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	// 创建行为树组件
	BehaviorTreeComp = CreateDefaultSubobject<UBehaviorTreeComponent>(TEXT("BehaviorTreeComponent"));
	BlackboardComp = CreateDefaultSubobject<UBlackboardComponent>(TEXT("BlackboardComponent"));

	SightConfig= CreateDefaultSubobject<UAISenseConfig_Sight>(TEXT("EnemySenseConfig_Sight"));
	SightConfig->SightRadius = 5000.0f; //视野范围
	SightConfig->LoseSightRadius = 6000.0f; //失去视野范围
	SightConfig->DetectionByAffiliation.bDetectEnemies = true;
	SightConfig->DetectionByAffiliation.bDetectFriendlies = true;
	SightConfig->DetectionByAffiliation.bDetectNeutrals = true;
	SightConfig->PeripheralVisionAngleDegrees=360.f; //视野角度

	EnemyPerceptionComponent= CreateDefaultSubobject<UAIPerceptionComponent>(TEXT("AIPerceptionComponent"));
	EnemyPerceptionComponent->SetDominantSense(UAISenseConfig_Sight::StaticClass());
	EnemyPerceptionComponent->ConfigureSense(*SightConfig);
	EnemyPerceptionComponent->OnTargetPerceptionUpdated.AddUniqueDynamic(this, &ThisClass::OnEnemyPerceptionUpdated);

}

void AWX_EnemyAIController::OnPossess(APawn* InPawn)
{
	Super::OnPossess(InPawn);
	EventSystem = GetGameInstance()->GetSubsystem<UWX_EventSystem>();
	// 创建事件载荷
	FGameplayTag BattleEventId = FGameplayTag::RequestGameplayTag(FName("Event.Battle.EnterBattle"));
	Payload = EventSystem->CreateEventPayload(EEventType::Combat, BattleEventId);
	Payload->EventSource = this;
}

// Called when the game starts or when spawned
void AWX_EnemyAIController::BeginPlay()
{
	Super::BeginPlay();
}

void AWX_EnemyAIController::OnEnemyPerceptionUpdated(AActor* Actor, FAIStimulus stimulus)
{
	AWX_CharacterController* PlayerController =
		Cast<AWX_CharacterController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));
	
	
	if (!EventSystem)
	{
		UE_LOG(LogTemp, Warning, TEXT("EventSystem is not initialized!"));
		return;
	}
	
	GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Red, FString::Printf(TEXT("EnemyAIController Perception Updated: %s"), *Actor->GetName()));
	if (!PlayerController)
	{
		return;
	}
	//发现进战
	EventSystem->TriggerEvent(Payload);
	// for (AWX_CharacterUnitBase* Units:PlayerController->GetSelectedUnits())
	// {
	// 	if (!Units->isInBattle)
	// 	{
	// 		Units->isInBattle=true;
	// 		PlayerController->GetBattleMagComponent()->BattlePlayerUnits.Add(Units);
	// 		PlayerController->GetBattleMagComponent()->AllBattleUnits.Add(Units);
	//
	// 		//获取allunits，并且按照插入排序插入到序列中
	// 		PlayerController->GetBattleMagComponent()->InsertBattleUnitSorted(Units);
	// 	}
	// }
}

void AWX_EnemyAIController::InitializeAI()
{
	CurrentActionPoints=CharacterAttribute.ActionPoints;
}

void AWX_EnemyAIController::StartBehaviorTree()
{
	if (BehaviorTree && BlackboardComp)
	{
		// 启动黑板
		UseBlackboard(BehaviorTree->BlackboardAsset,BlackboardComp);

		// 更新黑板数据
		UpdateBlackboardData();

		// 启动行为树
		RunBehaviorTree(BehaviorTree);

		UE_LOG(LogTemp, Warning, TEXT("行为树已启动: %s"), *BehaviorTree->GetName());
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("无法启动行为树 - BehaviorTree或BlackboardComp为空"));
	}
}

void AWX_EnemyAIController::StopBehaviorTree()
{
	if (BehaviorTreeComp)
	{
		BehaviorTreeComp->StopTree();
		UE_LOG(LogTemp, Warning, TEXT("行为树已停止"));
	}
}

void AWX_EnemyAIController::UpdateBlackboardData()
{
	if (!BlackboardComp)
		return;

	// 更新自身单位引用
	AWX_EnemyUnit* ControlledUnit = GetControlledEnemyUnit();
	if (ControlledUnit)
	{
		BlackboardComp->SetValueAsObject(TEXT("SelfActor"), ControlledUnit);
		BlackboardComp->SetValueAsInt(TEXT("CurrentActionPoints"), ControlledUnit->CurrentActionPoints);
		BlackboardComp->SetValueAsVector(TEXT("SelfLocation"), ControlledUnit->GetActorLocation());
	}

	// 更新最近的敌人
	AWX_CharacterUnitBase* NearestEnemy = GetNearestEnemy();
	if (NearestEnemy)
	{
		BlackboardComp->SetValueAsObject(TEXT("TargetEnemy"), NearestEnemy);
		BlackboardComp->SetValueAsVector(TEXT("TargetLocation"), NearestEnemy->GetActorLocation());

		// 计算到目标的距离
		if (ControlledUnit)
		{
			float Distance = FVector::Dist(ControlledUnit->GetActorLocation(), NearestEnemy->GetActorLocation());
			BlackboardComp->SetValueAsFloat(TEXT("DistanceToTarget"), Distance);
		}
	}
	else
	{
		BlackboardComp->ClearValue(TEXT("TargetEnemy"));
		BlackboardComp->ClearValue(TEXT("TargetLocation"));
	}
}

AWX_CharacterUnitBase* AWX_EnemyAIController::GetNearestEnemy()
{
	TArray<AActor*> BattleMagActor;
	UGameplayStatics::GetAllActorsOfClass(GetWorld(),AWX_BattleMag::StaticClass(),BattleMagActor);
	AWX_BattleMag* BattleMag =Cast<AWX_BattleMag>(BattleMagActor[0]) ;
	if (!BattleMag)
		return nullptr;

	AWX_EnemyUnit* ControlledUnit = GetControlledEnemyUnit();
	if (!ControlledUnit)
		return nullptr;

	AWX_CharacterUnitBase* NearestEnemy = nullptr;
	float MinDistance = FLT_MAX;

	// 遍历所有玩家单位，找到最近的
	for (AWX_CharacterUnitBase* PlayerUnit : BattleMag->BattlePlayerUnits)
	{
		if (PlayerUnit && !PlayerUnit->bIsDead)
		{
			float Distance = FVector::Dist(ControlledUnit->GetActorLocation(), PlayerUnit->GetActorLocation());
			if (Distance < MinDistance)
			{
				MinDistance = Distance;
				NearestEnemy = PlayerUnit;
			}
		}
	}

	return NearestEnemy;
}

FVector AWX_EnemyAIController::GetBestAttackPosition(AWX_CharacterUnitBase* Target)
{
	if (!Target)
		return FVector::ZeroVector;

	AWX_EnemyUnit* ControlledUnit = GetControlledEnemyUnit();
	if (!ControlledUnit)
		return FVector::ZeroVector;

	// 简单实现：返回目标周围的一个位置
	FVector TargetLocation = Target->GetActorLocation();
	FVector Direction = (ControlledUnit->GetActorLocation() - TargetLocation).GetSafeNormal();

	// 攻击距离（这里假设近战攻击距离为150单位）
	float AttackRange = 150.0f;

	return TargetLocation + Direction * AttackRange;
}

bool AWX_EnemyAIController::CanAttackTarget(AWX_CharacterUnitBase* Target)
{
	if (!Target)
		return false;

	AWX_EnemyUnit* ControlledUnit = GetControlledEnemyUnit();
	if (!ControlledUnit)
		return false;

	// 检查目标是否还活着
	if (Target->bIsDead)
		return false;

	// 检查距离是否在攻击范围内
	float Distance = FVector::Dist(ControlledUnit->GetActorLocation(), Target->GetActorLocation());
	float AttackRange = 200.0f; // 攻击范围

	return Distance <= AttackRange;
}

bool AWX_EnemyAIController::HasEnoughActionPointsToMove(const FVector& TargetLocation)
{
	AWX_EnemyUnit* ControlledUnit = GetControlledEnemyUnit();
	if (!ControlledUnit)
		return false;

	float Distance = FVector::Dist(ControlledUnit->GetActorLocation(),TargetLocation);
	int32 RequiredActionPoints = ControlledUnit->CalculateActionPointsForDistance(Distance);

	return ControlledUnit->CurrentActionPoints >= RequiredActionPoints;
}

AWX_EnemyUnit* AWX_EnemyAIController::GetControlledEnemyUnit()
{
	return Cast<AWX_EnemyUnit>(GetPawn());
}

