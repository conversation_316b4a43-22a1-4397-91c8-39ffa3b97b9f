// Fill out your copyright notice in the Description page of Project Settings.


#include "Mechanism/EnterBattleCollision.h"
#include "Character/WX_CharacterController.h"
#include "Character/WX_CharacterUnitBase.h"
#include "Character/WX_PlayerCharacterUnit.h"
#include "Component/WX_BattleMag.h"
#include "Components/BoxComponent.h"
#include "Kismet/GameplayStatics.h"


// Sets default values
AEnterBattleCollision::AEnterBattleCollision()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	BattleCollisionBox= CreateDefaultSubobject<UBoxComponent>(TEXT("BattleCollisionBox"));
	BattleCollisionBox->SetupAttachment(RootComponent);
	BattleCollisionBox->SetBoxExtent(FVector(100.0f, 100.0f, 100.0f));
	BattleCollisionBox->SetCollisionProfileName(FName("OverlapAllDynamic"));
	BattleCollisionBox->SetGenerateOverlapEvents(true);
	BattleCollisionBox->OnComponentBeginOverlap.AddDynamic(this, &AEnterBattleCollision::OnOverlapBegin);
}

// Called when the game starts or when spawned
void AEnterBattleCollision::BeginPlay()
{
	Super::BeginPlay();
	
}


void AEnterBattleCollision::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
	UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	AWX_CharacterController* PlayerController =
		Cast<AWX_CharacterController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));

	for (AWX_CharacterUnitBase* Units:PlayerController->GetSelectedUnits())
	{
		if (!Units->isInBattle)
		{
			Units->isInBattle=true;
			// PlayerController->GetBattleMagComponent()->BattlePlayerUnits.Add(Units);
			// PlayerController->GetBattleMagComponent()->AllBattleUnits.Add(Units);
			//
			// //获取allunits，并且按照插入排序插入到序列中
			// PlayerController->GetBattleMagComponent()->InsertBattleUnitSorted(Units);
		}
	}
	
}