// Fill out your copyright notice in the Description page of Project Settings.

#include "AI/BTService_WX_UpdateBlackboard.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "Enemy/WX_EnemyAIController.h"
#include "Enemy/WX_EnemyUnit.h"
#include "Character/WX_CharacterUnitBase.h"

UBTService_WX_UpdateBlackboard::UBTService_WX_UpdateBlackboard()
{
	NodeName = TEXT("WX Update Blackboard");
	Interval = 0.5f; // 每0.5秒更新一次
	RandomDeviation = 0.1f; // 随机偏差0.1秒

	// 设置黑板键过滤器
	NearestEnemyKey.AddObjectFilter(this, GET_MEMBER_NAME_CHECKED(UBTService_WX_UpdateBlackboard, NearestEnemyKey), AActor::StaticClass());
	TargetLocationKey.AddVectorFilter(this, GET_MEMBER_NAME_CHECKED(UBTService_WX_UpdateBlackboard, TargetLocationKey));
	DistanceToTargetKey.AddFloatFilter(this, GET_MEMBER_NAME_CHECKED(UBTService_WX_UpdateBlackboard, DistanceToTargetKey));
	CurrentActionPointsKey.AddIntFilter(this, GET_MEMBER_NAME_CHECKED(UBTService_WX_UpdateBlackboard, CurrentActionPointsKey));
	SelfLocationKey.AddVectorFilter(this, GET_MEMBER_NAME_CHECKED(UBTService_WX_UpdateBlackboard, SelfLocationKey));
}

void UBTService_WX_UpdateBlackboard::TickNode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds)
{
	Super::TickNode(OwnerComp, NodeMemory, DeltaSeconds);

	AWX_EnemyAIController* AIController = Cast<AWX_EnemyAIController>(OwnerComp.GetAIOwner());
	if (!AIController)
	{
		return;
	}

	UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
	if (!BlackboardComp)
	{
		return;
	}

	// 更新最近敌人信息
	if (bUpdateNearestEnemy)
	{
		UpdateNearestEnemyInfo(AIController, BlackboardComp);
	}

	// 更新自身状态信息
	if (bUpdateSelfStatus)
	{
		UpdateSelfStatusInfo(AIController, BlackboardComp);
	}
}

FString UBTService_WX_UpdateBlackboard::GetStaticDescription() const
{
	TArray<FString> UpdateTypes;
	
	if (bUpdateNearestEnemy)
		UpdateTypes.Add(TEXT("最近敌人"));
	if (bUpdateSelfStatus)
		UpdateTypes.Add(TEXT("自身状态"));
	if (bUpdateBattlefieldInfo)
		UpdateTypes.Add(TEXT("战场信息"));

	return FString::Printf(TEXT("更新黑板数据\n更新类型: %s\n更新间隔: %.1f秒"), 
		*FString::Join(UpdateTypes, TEXT(", ")),
		Interval);
}

void UBTService_WX_UpdateBlackboard::UpdateNearestEnemyInfo(AWX_EnemyAIController* AIController, UBlackboardComponent* BlackboardComp)
{
	if (!AIController || !BlackboardComp)
		return;

	// 获取最近的敌人
	AWX_CharacterUnitBase* NearestEnemy = AIController->GetNearestEnemy();
	
	if (NearestEnemy)
	{
		// 更新最近敌人
		if (NearestEnemyKey.SelectedKeyName != NAME_None)
		{
			BlackboardComp->SetValueAsObject(NearestEnemyKey.SelectedKeyName, NearestEnemy);
		}

		// 更新目标位置
		if (TargetLocationKey.SelectedKeyName != NAME_None)
		{
			BlackboardComp->SetValueAsVector(TargetLocationKey.SelectedKeyName, NearestEnemy->GetActorLocation());
		}

		// 更新到目标的距离
		if (DistanceToTargetKey.SelectedKeyName != NAME_None)
		{
			AWX_EnemyUnit* ControlledUnit = AIController->GetControlledEnemyUnit();
			if (ControlledUnit)
			{
				float Distance = FVector::Dist(ControlledUnit->GetActorLocation(), NearestEnemy->GetActorLocation());
				BlackboardComp->SetValueAsFloat(DistanceToTargetKey.SelectedKeyName, Distance);
			}
		}
	}
	else
	{
		// 清除目标信息
		if (NearestEnemyKey.SelectedKeyName != NAME_None)
		{
			BlackboardComp->ClearValue(NearestEnemyKey.SelectedKeyName);
		}
		if (TargetLocationKey.SelectedKeyName != NAME_None)
		{
			BlackboardComp->ClearValue(TargetLocationKey.SelectedKeyName);
		}
		if (DistanceToTargetKey.SelectedKeyName != NAME_None)
		{
			BlackboardComp->ClearValue(DistanceToTargetKey.SelectedKeyName);
		}
	}
}

void UBTService_WX_UpdateBlackboard::UpdateSelfStatusInfo(AWX_EnemyAIController* AIController, UBlackboardComponent* BlackboardComp)
{
	if (!AIController || !BlackboardComp)
		return;

	AWX_EnemyUnit* ControlledUnit = AIController->GetControlledEnemyUnit();
	if (!ControlledUnit)
		return;

	// 更新当前行动点
	if (CurrentActionPointsKey.SelectedKeyName != NAME_None)
	{
		BlackboardComp->SetValueAsInt(CurrentActionPointsKey.SelectedKeyName, ControlledUnit->CurrentActionPoints);
	}

	// 更新自身位置
	if (SelfLocationKey.SelectedKeyName != NAME_None)
	{
		BlackboardComp->SetValueAsVector(SelfLocationKey.SelectedKeyName, ControlledUnit->GetActorLocation());
	}
}
