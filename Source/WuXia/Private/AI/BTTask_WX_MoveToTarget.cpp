// Fill out your copyright notice in the Description page of Project Settings.

#include "AI/BTTask_WX_MoveToTarget.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "Enemy/WX_EnemyAIController.h"
#include "Enemy/WX_EnemyUnit.h"
#include "Character/WX_CharacterUnitBase.h"
#include "AIController.h"
#include "Navigation/PathFollowingComponent.h"

UBTTask_WX_MoveToTarget::UBTTask_WX_MoveToTarget()
{
	NodeName = TEXT("WX Move To Target");
	
	// 设置黑板键过滤器
	TargetActorKey.AddObjectFilter(this, GET_MEMBER_NAME_CHECKED(UBTTask_WX_MoveToTarget, TargetActorKey), AActor::StaticClass());
}

void UBTTask_WX_MoveToTarget::OnMoveFinished(FAIRequestID FaiRequestID, const FPathFollowingResult& PathFollowingResult)
{
	//临时
	// 通过事件系统通知回合结束
	UWX_EventSystem* EventSystem = AIController->GetGameInstance()->GetSubsystem<UWX_EventSystem>();
	if (EventSystem)
	{
		// 创建下一角色事件
		FGameplayTag NextCharacterTag = FGameplayTag::RequestGameplayTag(FName("Event.Battle.NextCharacter"));
		UWX_EventPayload* Payload = EventSystem->CreateEventPayload(EEventType::Combat, NextCharacterTag);
		if (Payload)
		{
			Payload->EventSource = AIController;
			EventSystem->TriggerEvent(Payload);
		}
	}
}

EBTNodeResult::Type UBTTask_WX_MoveToTarget::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	AIController = Cast<AWX_EnemyAIController>(OwnerComp.GetAIOwner());
	if (!AIController)
	{
		UE_LOG(LogTemp, Error, TEXT("BTTask_WX_MoveToTarget: 无效的AI控制器"));
		return EBTNodeResult::Failed;
	}

	// subscribe to the move finished handler on the path following component
	UPathFollowingComponent* PFComp = AIController->GetPathFollowingComponent();
	if (PFComp)
	{
		PFComp->OnRequestFinished.AddUObject(this, &UBTTask_WX_MoveToTarget::OnMoveFinished);
	}
	AWX_EnemyUnit* ControlledUnit = AIController->GetControlledEnemyUnit();
	if (!ControlledUnit)
	{
		UE_LOG(LogTemp, Error, TEXT("BTTask_WX_MoveToTarget: 无效的控制单位"));
		return EBTNodeResult::Failed;
	}

	// 从黑板获取目标
	UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
	AWX_CharacterUnitBase* Target = Cast<AWX_CharacterUnitBase>(BlackboardComp->GetValueAsObject(TargetActorKey.SelectedKeyName));
	
	if (!Target)
	{
		UE_LOG(LogTemp, Warning, TEXT("BTTask_WX_MoveToTarget: 没有找到有效目标"));
		return EBTNodeResult::Failed;
	}
	
	//获取能到达的最远位置
	ControlledUnit->CanReachMaxLocation(Target->GetActorLocation());

	//？？到达最远位置再执行相关攻击逻辑
	if (bUseOptimalAttackPosition)
	{
		Target->CanReachTargetLocation = CalculateOptimalMovePosition(Target);
	}

	// 执行移动
	FAIMoveRequest MoveRequest;
	MoveRequest.SetGoalLocation(Target->CanReachTargetLocation);
	MoveRequest.SetAcceptanceRadius(AcceptanceRadius);
	MoveRequest.SetUsePathfinding(true);

	FNavPathSharedPtr Path;
	const FPathFollowingRequestResult RequestResult = AIController->MoveTo(MoveRequest, &Path);

	if (RequestResult.Code == EPathFollowingRequestResult::RequestSuccessful)
	{
		UE_LOG(LogTemp, Log, TEXT("BTTask_WX_MoveToTarget: 开始移动到目标位置"));
		return EBTNodeResult::InProgress;
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("BTTask_WX_MoveToTarget: 移动请求失败"));
		return EBTNodeResult::Failed;
	}

	//判断是否已经移动到目的地
	return EBTNodeResult::Succeeded;
}

void UBTTask_WX_MoveToTarget::OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult)
{
	AIController = Cast<AWX_EnemyAIController>(OwnerComp.GetAIOwner());
	if (AIController)
	{
		AIController->StopMovement();
		
		if (TaskResult == EBTNodeResult::Succeeded)
		{
			UE_LOG(LogTemp, Log, TEXT("BTTask_WX_MoveToTarget: 移动完成"));
			
			// 更新黑板数据
			AIController->UpdateBlackboardData();
		}
	}

	Super::OnTaskFinished(OwnerComp, NodeMemory, TaskResult);
}

FString UBTTask_WX_MoveToTarget::GetStaticDescription() const
{
	return FString::Printf(TEXT("移动到目标: %s\n接受半径: %.1f\n使用最佳攻击位置: %s"), 
		*TargetActorKey.SelectedKeyName.ToString(),
		AcceptanceRadius,
		bUseOptimalAttackPosition ? TEXT("是") : TEXT("否"));
}

FVector UBTTask_WX_MoveToTarget::CalculateOptimalMovePosition(AWX_CharacterUnitBase* Target)
{
	if (!AIController || !Target)
		return FVector::ZeroVector;

	// 使用AI控制器的方法计算最佳攻击位置
	return AIController->GetBestAttackPosition(Target);
}
