// Fill out your copyright notice in the Description page of Project Settings.

#include "AI/BTTask_WX_EndTurn.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "Enemy/WX_EnemyAIController.h"
#include "Enemy/WX_EnemyUnit.h"
#include "Component/WX_BattleMag.h"
#include "Event/WX_EventSystem.h"
#include "Kismet/GameplayStatics.h"

UBTTask_WX_EndTurn::UBTTask_WX_EndTurn()
{
	NodeName = TEXT("WX End Turn");
}

EBTNodeResult::Type UBTTask_WX_EndTurn::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	AWX_EnemyAIController* AIController = Cast<AWX_EnemyAIController>(OwnerComp.GetAIOwner());
	if (!AIController)
	{
		UE_LOG(LogTemp, Error, TEXT("BTTask_WX_EndTurn: 无效的AI控制器"));
		return EBTNodeResult::Failed;
	}

	AWX_EnemyUnit* ControlledUnit = AIController->GetControlledEnemyUnit();
	if (ControlledUnit)
	{
		UE_LOG(LogTemp, Log, TEXT("BTTask_WX_EndTurn: %s 结束回合，剩余行动点: %d"), 
			*ControlledUnit->GetName(), ControlledUnit->CurrentActionPoints);
	}

	// 停止行为树
	if (bStopBehaviorTree)
	{
		AIController->StopBehaviorTree();
	}

	// 通知回合结束
	if (bTriggerNextCharacter)
	{
		NotifyTurnEnd(AIController);
	}

	return EBTNodeResult::Succeeded;
}

FString UBTTask_WX_EndTurn::GetStaticDescription() const
{
	TArray<FString> Actions;
	
	if (bStopBehaviorTree)
		Actions.Add(TEXT("停止行为树"));
	if (bTriggerNextCharacter)
		Actions.Add(TEXT("触发下一角色"));

	return FString::Printf(TEXT("结束回合\n动作: %s"), 
		*FString::Join(Actions, TEXT(", ")));
}

void UBTTask_WX_EndTurn::NotifyTurnEnd(AWX_EnemyAIController* AIController)
{
	if (!AIController)
		return;

	// 通过事件系统通知回合结束
	UWX_EventSystem* EventSystem = AIController->GetGameInstance()->GetSubsystem<UWX_EventSystem>();
	if (EventSystem)
	{
		// 创建下一角色事件
		FGameplayTag NextCharacterTag = FGameplayTag::RequestGameplayTag(FName("Event.Battle.NextCharacter"));
		UWX_EventPayload* Payload = EventSystem->CreateEventPayload(EEventType::Combat, NextCharacterTag);
		if (Payload)
		{
			Payload->EventSource = AIController;
			EventSystem->TriggerEvent(Payload);
		}
	}
}
