// Fill out your copyright notice in the Description page of Project Settings.


#include "Component/WX_BattleMag.h"
#include "Character/WX_CharacterController.h"
#include "Character/WX_CharacterUnitBase.h"
#include "Character/WX_PlayerCharacterUnit.h"
#include "Enemy/WX_EnemyAIController.h"
#include "Enemy/WX_EnemyUnit.h"
#include "Event/WX_EventSystem.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"


// Sets default values for this component's properties
AWX_BattleMag::AWX_BattleMag()
{
}


// Called when the game starts
void AWX_BattleMag::BeginPlay()
{
	Super::BeginPlay();
	PawnController=Cast<AWX_CharacterController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));
	EventSystem = GetGameInstance()->GetSubsystem<UWX_EventSystem>();
	if (EventSystem)
	{
		FGameplayTag EnterBattleTag = FGameplayTag::RequestGameplayTag(FName("Event.Battle.EnterBattle"));
		FGameplayTag NextCharacterTag = FGameplayTag::RequestGameplayTag(FName("Event.Battle.NextCharacter"));
		EventSystem->RegisterEventById(EnterBattleTag, this, &AWX_BattleMag::EnterBattleTurn);
		EventSystem->RegisterEventById(NextCharacterTag,this,&AWX_BattleMag::NextCharacterReady);
	}
}

//通过AI感知触发，对话触发
void AWX_BattleMag::EnterBattleTurn(UWX_EventPayload* Payload)
{
	//生成一个overspherelap获取所有Units，并分类
	
		TArray<AActor*> OverlappedActors;
		if (PawnController)
		{
			TArray<TEnumAsByte<EObjectTypeQuery>> ObjectTypes;
			ObjectTypes.Add(UEngineTypes::ConvertToObjectType(ECollisionChannel::ECC_Pawn));
		
			UKismetSystemLibrary::SphereOverlapActors(
				this,
				PawnController->GetSelectedUnits()[0]->GetActorLocation(),
				BattleEnterRadius,
				ObjectTypes,
				ACharacter::StaticClass(),
				TArray<AActor*>(),
				OverlappedActors
			);

			if (IsEnterDebugSphere)
			{
				DrawDebugSphere(
					GetWorld(),
				PawnController->GetSelectedUnits()[0]->GetActorLocation(),
					BattleEnterRadius,
					32,                       // 球体分段数（越大越圆）
					FColor::Red,
					false,                   // 不永久显示
					 5.f,                     // 显示时间，秒
			0,
					1.0f                     // 线宽
				);
			}
		
		}

		//对OverlappedActors进行分类
		for (AActor* units:OverlappedActors)
		{
			AWX_CharacterUnitBase* Unit=Cast<AWX_CharacterUnitBase>(units);
			if (Unit)
			{
				Unit->CharacterState= ECharacterState::Combat;
				AllBattleUnits.Add(Unit);
				//简单分类，后续可以根据阵营等复杂条件分类
				if (Unit->CharacterType==ECharacterType::Player&&!BattlePlayerUnits.Contains(Unit))
				{
					BattlePlayerUnits.Add(Unit);
				}
				if (Unit->CharacterType==ECharacterType::Enemy&&!BattleEnemyUnits.Contains(Unit))
				{
					BattleEnemyUnits.Add(Unit);
				}
			}
		}

		GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, FString::Printf(TEXT("Enter Battle Turn: %d Player Units, %d Enemy Units"), BattlePlayerUnits.Num(), BattleEnemyUnits.Num()));
		
		//根据速度对AllbattleUnits排序，快的在前面
		AllBattleUnits.Sort([](const AWX_CharacterUnitBase& A, const AWX_CharacterUnitBase& B)
		{
			//身法
			return A.CharacterAttribute.Dexterity > B.CharacterAttribute.Dexterity;
		});

		//开始对allbattleunits进行逐一递增，并判断是玩家回合还是AI回合，执行对应函数
	if (AllBattleUnits.Num()>0)
	{
		if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Player)
		{
			AWX_PlayerCharacterUnit* CurrentUnit=Cast<AWX_PlayerCharacterUnit>(AllBattleUnits[CurrentCharacterIndex]);
			PawnController->TargetUnit=CurrentUnit;
			PawnController->PreviousTargetUnit=CurrentUnit;
			PlayerTurn(CurrentUnit);
		}
		else if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Enemy)
		{
			AWX_EnemyUnit* CurrentUnit=Cast<AWX_EnemyUnit>(AllBattleUnits[CurrentCharacterIndex]);
			AITurn(CurrentUnit);
		}
	}
	
	Payload->EventId=FGameplayTag::RequestGameplayTag(FName("Event.Battle.BattleUI"));
	Payload->EventSource=this;
	Payload->EventType=EEventType::Combat;
	EventSystem->TriggerEvent(Payload);
		
}


void AWX_BattleMag::PlayerTurn(AWX_PlayerCharacterUnit* CurrentUnit)
{
	CurrentUnit->IsBeenSelected=true;
}

void AWX_BattleMag::AITurn(AWX_EnemyUnit* CurrentUnit)
{
	//执行行为树
	if (CurrentUnit)
	{
		// 获取AI控制器
		AWX_EnemyAIController* AIController = Cast<AWX_EnemyAIController>(CurrentUnit->GetController());
		if (AIController)
		{
			// 初始化AI（恢复行动点等）
			AIController->InitializeAI();

			// 启动行为树
			AIController->StartBehaviorTree();

			UE_LOG(LogTemp, Log, TEXT("AI回合开始: %s"), *CurrentUnit->GetName());
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("AI单位没有有效的AI控制器: %s"), *CurrentUnit->GetName());
			// 如果没有AI控制器，直接跳过这个回合
			// 这里可以触发下一个角色的回合
			
		}
	}
}

void AWX_BattleMag::NextCharacterReady(UWX_EventPayload* Payload)
{
	PawnController->PreviousTargetUnit->IsBeenSelected=false;
	CurrentCharacterIndex++;
	if (CurrentCharacterIndex>=AllBattleUnits.Num())
	{
		CurrentCharacterIndex=0;
	}
	if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Player)
	{
		//恢复行动力
		if (AllBattleUnits[CurrentCharacterIndex]->CurrentActionPoints==0)
		{
			AllBattleUnits[CurrentCharacterIndex]->CurrentActionPoints=AllBattleUnits[CurrentCharacterIndex]->CharacterAttribute.ActionPoints;
		}
		AWX_PlayerCharacterUnit* CurrentUnit=Cast<AWX_PlayerCharacterUnit>(AllBattleUnits[CurrentCharacterIndex]);
		PawnController->TargetUnit=CurrentUnit;
		PawnController->PreviousTargetUnit=CurrentUnit;
		PlayerTurn(CurrentUnit);
	}
	else if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Enemy)
	{
		AWX_EnemyUnit* CurrentUnit=Cast<AWX_EnemyUnit>(AllBattleUnits[CurrentCharacterIndex]);
		AITurn(CurrentUnit);
		NextCharacterReady(Payload);
	}
}

void AWX_BattleMag::EndBattleTurn()
{
	//脱战
}

void AWX_BattleMag::MeleeAttack(AWX_CharacterUnitBase* Attacker, AWX_CharacterUnitBase* Target)
{
}

void AWX_BattleMag::RangedAttack(AWX_CharacterUnitBase* Attacker, AWX_CharacterUnitBase* Target)
{
}

void AWX_BattleMag::CheckOutBattleProximity(AWX_PlayerCharacterUnit* CurrentUnit)
{
	if (!CurrentUnit)
	{
		return;
	}
	if (BattleEnemyUnits.Num()==0)
	{
		//切换为探索模式
		AllBattleUnits.Empty();
		BattlePlayerUnits.Empty();
		return;
	}

	bool bIsPlayer = BattlePlayerUnits.Contains(CurrentUnit);
	bool bIsEnemy = BattleEnemyUnits.Contains(CurrentUnit);
	if (!bIsPlayer && !bIsEnemy) return;

	const TArray<AWX_CharacterUnitBase*>& OpponentGroup = bIsPlayer ? BattleEnemyUnits : BattlePlayerUnits;

	bool bInBattleRange = false;

	for (AWX_CharacterUnitBase* Opponent : OpponentGroup)
	{
		if (!Opponent) continue;

		float Distance = FVector::Dist(CurrentUnit->GetActorLocation(), Opponent->GetActorLocation());
		if (Distance <= BattleDistance)
		{
			bInBattleRange = true;
			break;
		}
	}

	if (!bInBattleRange)
	{
		if (bIsPlayer)
		{
			//发消息表示玩家角色脱战
			
		}
		else if (bIsEnemy)
		{
			//发消息表示enemy逃跑
			
		}

		AllBattleUnits.Remove(CurrentUnit);
		BattlePlayerUnits.Remove(CurrentUnit);
		BattleEnemyUnits.Remove(CurrentUnit);
	}
}

void AWX_BattleMag::InsertBattleUnitSorted(AWX_CharacterUnitBase* CurrentUnit)
{
	if (!CurrentUnit) return;

	// 假设你已有接口或方法可以获取 initiative 值
	float NewInit = CurrentUnit->CharacterAttribute.Dexterity;

	int32 InsertIndex = 0;
	for (; InsertIndex < AllBattleUnits.Num(); ++InsertIndex)
	{
		AWX_PlayerCharacterUnit* ExistingUnit =Cast<AWX_PlayerCharacterUnit>(AllBattleUnits[InsertIndex]) ;
		if (!ExistingUnit) continue;

		float ExistingInit = ExistingUnit->CharacterAttribute.Dexterity;

		// 假设你希望 Initiative 值高的排在前面
		if (NewInit > ExistingInit)
		{
			break;
		}
	}

	AllBattleUnits.Insert(CurrentUnit, InsertIndex);
}